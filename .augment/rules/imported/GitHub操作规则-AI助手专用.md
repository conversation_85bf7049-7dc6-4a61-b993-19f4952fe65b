---
type: "agent_requested"
description: "执行GitHub相关操作时，包括但不限于：GitHub Projects管理、Issue/PR操作、Workflow触发、自动化系统维护等任务"
---
# GitHub操作规则 - Augment AI助手专用

**版本：** v1.0  
**适用范围：** 柴管家项目GitHub自动化系统  
**更新日期：** 2025-08-02

---

## 🎯 核心原则

### P1. 系统兼容性原则
- **必须**与现有GitHub Projects自动化系统保持完全兼容
- **禁止**执行可能破坏自动化流程的操作
- **优先**使用已验证的API调用模式和参数

### P2. 个人项目优先原则
- **默认**所有操作针对个人GitHub Projects（非组织项目）
- **必须**使用`user(login: $owner)`而非`organization(login: $owner)`
- **禁止**尝试访问组织级别的API端点

### P3. 认证安全原则
- **仅使用**Personal Access Token (CHAIGUANJIA_PAT)
- **禁止**使用GitHub App认证方式
- **严格**遵守Secret命名规范（不得以GITHUB_开头）

---

## 📋 操作规则分类

## 1. GitHub Projects操作规则

### 1.1 项目识别和访问 [RULE-GP-001]

**操作场景：** 访问或查询GitHub Projects

**必需要求：**
```bash
# ✅ 正确：个人项目访问
PROJECT_OWNER="Amoresdk"
PROJECT_TYPE="user"
PROJECT_NUMBER=1

# ❌ 错误：组织项目访问
PROJECT_OWNER="SomeOrg"
PROJECT_TYPE="organization"
```

**GraphQL查询格式：**
```graphql
# ✅ 必须使用的格式
query($owner: String!, $number: Int!) {
  user(login: $owner) {
    projectV2(number: $number) {
      id
      title
    }
  }
}

# ❌ 禁止使用的格式
query($owner: String!, $number: Int!) {
  organization(login: $owner) {
    projectV2(number: $number) {
      id
      title
    }
  }
}
```

**禁止事项：**
- 不得尝试访问`/orgs/{owner}/projects`路径
- 不得使用organization相关的GraphQL查询

### 1.2 项目字段更新 [RULE-GP-002]

**操作场景：** 更新项目条目的状态字段

**标准状态值：**
- `Todo` - 待办状态
- `In Progress` - 进行中状态  
- `Done` - 已完成状态

**必需的Mutation格式：**
```graphql
# ✅ 正确的mutation名称
mutation($projectId: ID!, $itemId: ID!, $fieldId: ID!, $optionId: String!) {
  updateProjectV2ItemFieldValue(input: {
    projectId: $projectId
    itemId: $itemId
    fieldId: $fieldId
    value: {
      singleSelectOptionId: $optionId
    }
  }) {
    projectV2Item {
      id
    }
  }
}

# ❌ 错误的mutation名称（已废弃）
addProjectV2ItemByContentId
```

**禁止事项：**
- 不得使用已废弃的API方法
- 不得跳过字段ID验证直接更新

### 1.3 条目添加规则 [RULE-GP-003]

**操作场景：** 将Issue或PR添加到项目

**正确的添加流程：**
```graphql
# 步骤1：添加条目到项目
mutation($projectId: ID!, $contentId: ID!) {
  addProjectV2ItemById(input: {
    projectId: $projectId
    contentId: $contentId
  }) {
    item {
      id
    }
  }
}

# 步骤2：设置初始状态为Todo
# 使用上面的updateProjectV2ItemFieldValue
```

**必需验证：**
- 确认项目ID有效
- 确认内容ID（Issue/PR的node_id）存在
- 验证操作权限

## 2. Issue和PR管理规则

### 2.1 Issue创建规范 [RULE-IP-001]

**标题格式要求：**
```
[类型] 简洁描述功能或问题

示例：
feat: 添加用户登录功能
fix: 修复数据导出错误
docs: 更新API文档
test: 添加单元测试
```

**必需标签：**
- 优先级标签：`priority:high|medium|low`
- 类型标签：`type:feature|bug|docs|test`
- 状态标签：`status:blocked|review`（可选）

**描述模板：**
```markdown
## 需求描述
[详细描述]

## 验收标准
- [ ] 标准1
- [ ] 标准2

## 技术要求
[技术栈、性能、兼容性要求]

## 相关资源
[设计稿、文档、相关Issue链接]
```

### 2.2 自动化触发规则 [RULE-IP-002]

**触发事件：**
- Issue创建 → 自动添加到项目，状态设为"Todo"
- Issue关闭 → 自动更新状态为"Done"
- PR合并 → 自动更新状态为"Done"

**AI助手操作约束：**
- **必须**等待自动化系统完成状态更新
- **禁止**手动干预正在进行的自动化流程
- **仅在**自动化失败时进行手动修复

### 2.3 状态同步规则 [RULE-IP-003]

**状态一致性要求：**
- GitHub Issue状态必须与Projects状态保持同步
- 发现不一致时，以Projects状态为准进行修正

**手动干预条件：**
- 自动化系统故障
- 特殊业务需求
- 紧急状态调整

## 3. Workflow和Actions操作规则

### 3.1 Workflow触发规则 [RULE-WF-001]

**手动触发标准方法：**
```bash
# ✅ 正确的触发方式
gh workflow run task-automation.yml -f operation=start-next-task
gh workflow run task-automation.yml -f operation=complete-task
gh workflow run task-automation.yml -f operation=sync-status

# ❌ 禁止的操作
# 不得直接修改workflow文件后立即触发
# 不得在workflow运行中强制取消
```

**参数验证要求：**
- 确认operation参数有效
- 验证当前系统状态适合执行操作
- 检查是否有冲突的运行中workflow

### 3.2 Secret配置规则 [RULE-WF-002]

**命名规范：**
```bash
# ✅ 正确的Secret名称
CHAIGUANJIA_PAT
CHAIGUANJIA_CONFIG
PROJECT_ACCESS_TOKEN

# ❌ 禁止的Secret名称
GITHUB_PAT          # 不能以GITHUB_开头
GITHUB_TOKEN        # 系统保留
GITHUB_WORKSPACE    # 系统保留
```

**配置要求：**
- PAT必须包含`repo`和`project`权限
- 定期检查token有效期
- 不得在日志中暴露token值

### 3.3 日志和调试规则 [RULE-WF-003]

**标准查看命令：**
```bash
# 查看workflow运行状态
gh run list --workflow=task-automation.yml --limit=5

# 查看详细日志
gh run view --log

# 查看特定job日志
gh run view <run-id> --job=<job-id> --log

# 错误过滤
gh run view --log | grep -E "(ERROR|FAIL|❌)"
```

**调试约束：**
- 不得在生产环境进行破坏性调试
- 必须使用dry-run模式进行测试
- 保留关键操作的日志记录

## 4. 技术实施约束

### 4.1 认证方式约束 [RULE-TI-001]

**强制要求：**
- **仅使用**Personal Access Token认证
- **禁止**尝试配置GitHub App
- **必须**验证token权限范围

**权限检查：**
```bash
# 验证认证状态
gh auth status

# 检查API访问权限
gh api graphql -f query='query { viewer { login } }'

# 验证项目访问权限
gh api graphql -f query='
  query($owner: String!, $number: Int!) {
    user(login: $owner) {
      projectV2(number: $number) { id }
    }
  }' -f owner="Amoresdk" -F number=1
```

### 4.2 API调用约束 [RULE-TI-002]

**GraphQL查询规范：**
- 必须声明所有使用的变量
- 使用`?`操作符处理可能的null值
- 添加适当的错误处理逻辑

**示例：**
```bash
# ✅ 正确的null值处理
jq -r '.data.user.projectV2.items.nodes[]? | select(...)'

# ❌ 错误的处理方式
jq -r '.data.user.projectV2.items.nodes[] | select(...)'
```

### 4.3 错误处理约束 [RULE-TI-003]

**必需的错误检查：**
- API响应状态验证
- JSON格式有效性检查
- 权限错误的特殊处理

**标准错误处理模式：**
```bash
# JSON有效性检查
if echo "$response" | jq . > /dev/null 2>&1; then
    # 处理有效JSON
else
    echo "❌ 无效的JSON响应"
    exit 1
fi

# API错误检查
if echo "$response" | jq -e '.errors' > /dev/null 2>&1; then
    echo "❌ GraphQL错误：$(echo "$response" | jq -r '.errors[0].message')"
    exit 1
fi
```

## 5. 团队协作规范

### 5.1 分支管理规则 [RULE-TC-001]

**分支命名格式：**
```
feature/issue<number>-<description>
fix/issue<number>-<description>
docs/issue<number>-<description>

示例：
feature/issue15-user-authentication
fix/issue23-data-export-error
docs/issue8-api-documentation
```

**操作约束：**
- 不得直接向main分支提交
- 必须通过PR进行代码合并
- 保持分支与Issue的对应关系

### 5.2 提交规范 [RULE-TC-002]

**Commit消息格式：**
```
<type>(<scope>): <subject>

type: feat|fix|docs|test|refactor
scope: api|ui|automation|config
subject: 中文描述（简洁明确）

示例：
feat(automation): 新增任务自动分配功能
fix(api): 修复GraphQL查询错误
docs(guide): 更新操作规则文档
```

**关联Issue：**
```
# 在commit消息中关联Issue
feat(api): 新增用户认证接口

Closes #15
```

### 5.3 文档维护规则 [RULE-TC-003]

**更新责任：**
- 修改自动化系统时必须同步更新文档
- 新增规则时必须更新本规则文档
- 保持代码示例与实际实现的一致性

**版本管理：**
- 重要变更必须更新版本号
- 保留变更历史记录
- 及时同步团队成员

---

## ✅ 执行前检查清单

### 操作前必检项目：
- [ ] 确认操作目标为个人项目（非组织项目）
- [ ] 验证CHAIGUANJIA_PAT Secret配置有效
- [ ] 检查当前是否有运行中的相关workflow
- [ ] 确认操作不会与自动化系统冲突
- [ ] 验证必需的权限和参数

### GraphQL操作检查：
- [ ] 使用正确的查询根节点（user vs organization）
- [ ] 声明所有使用的变量
- [ ] 添加null值处理（使用?操作符）
- [ ] 使用正确的mutation名称
- [ ] 验证字段ID和选项ID有效性

### 错误预防检查：
- [ ] JSON响应格式验证
- [ ] API错误状态检查
- [ ] 权限不足的处理方案
- [ ] 操作失败的回滚机制
- [ ] 日志记录和错误报告

---

## 🚨 常见错误预防

### 错误1：项目类型混淆
**症状：** 404错误或"Project not found"
**预防：** 始终使用`user(login: $owner)`查询个人项目

### 错误2：Secret命名错误
**症状：** "Secret not found"或认证失败
**预防：** 确保Secret名称不以"GITHUB_"开头

### 错误3：GraphQL变量未使用
**症状：** "Variable declared but not used"
**预防：** 移除查询中未使用的变量声明

### 错误4：null值处理错误
**症状：** "Cannot iterate over null"
**预防：** 在数组访问时使用`[]?`操作符

### 错误5：Mutation名称错误
**症状：** "Field doesn't exist"
**预防：** 使用`addProjectV2ItemById`而非已废弃的方法

---

## 📚 快速参考

### 常用GraphQL查询模板

**查询项目基本信息：**
```graphql
query($owner: String!, $number: Int!) {
  user(login: $owner) {
    projectV2(number: $number) {
      id
      title
      fields(first: 20) {
        nodes {
          ... on ProjectV2SingleSelectField {
            id
            name
            options {
              id
              name
            }
          }
        }
      }
    }
  }
}
```

**查询待办任务：**
```graphql
query($owner: String!, $number: Int!) {
  user(login: $owner) {
    projectV2(number: $number) {
      items(first: 50, orderBy: {field: POSITION, direction: ASC}) {
        nodes {
          id
          content {
            ... on Issue {
              id
              number
              title
              state
            }
          }
          fieldValues(first: 10) {
            nodes {
              ... on ProjectV2ItemFieldSingleSelectValue {
                name
                field {
                  ... on ProjectV2SingleSelectField {
                    name
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
```

### 标准操作命令

**项目状态查询：**
```bash
# 查看当前任务状态
./scripts/show-status.sh

# 同步项目状态
./scripts/sync-status.sh --dry-run

# 开始下一个任务
gh workflow run task-automation.yml -f operation=start-next-task
```

**故障排查命令：**
```bash
# 检查认证状态
gh auth status

# 验证项目访问
gh api graphql -f query='query($owner: String!, $number: Int!) {
  user(login: $owner) {
    projectV2(number: $number) { id title }
  }
}' -f owner="Amoresdk" -F number=1

# 查看最新workflow日志
gh run list --workflow=task-automation.yml --limit=1
gh run view --log | grep -E "(ERROR|❌|FAIL)"
```

### 配置参数快查

| 参数名 | 值 | 用途 |
|--------|-----|------|
| PROJECT_OWNER | Amoresdk | 项目所有者 |
| PROJECT_NUMBER | 1 | 项目编号 |
| PROJECT_TYPE | user | 项目类型（个人） |
| SECRET_NAME | CHAIGUANJIA_PAT | 访问令牌名称 |

### 状态字段映射

| 中文状态 | 英文状态 | 项目字段值 | 说明 |
|----------|----------|------------|------|
| 待办 | Todo | Todo | 新创建的任务 |
| 进行中 | In Progress | In Progress | 正在开发的任务 |
| 已完成 | Done | Done | 已完成的任务 |

---

## 🔄 操作流程图

### Issue创建到完成的标准流程：

```
1. 创建Issue (遵循标题格式和标签规范)
   ↓
2. 自动化系统添加到项目 (状态: Todo)
   ↓
3. 手动/自动开始任务 (状态: In Progress)
   ↓
4. 开发完成，关闭Issue
   ↓
5. 自动化系统更新状态 (状态: Done)
```

### AI助手介入点：

- **Issue创建时**：验证格式规范，添加必要标签
- **状态异常时**：手动修复同步问题
- **系统故障时**：执行故障排查和修复
- **批量操作时**：使用脚本进行批量状态更新

---

## ⚠️ 紧急情况处理

### 自动化系统故障

**症状识别：**
- Workflow连续失败
- 状态同步异常
- API调用错误

**处理步骤：**
1. 立即停止相关workflow
2. 检查Secret配置和权限
3. 验证GraphQL查询语法
4. 手动修复数据不一致
5. 重新启动自动化流程

**紧急修复命令：**
```bash
# 停止运行中的workflow
gh run cancel <run-id>

# 手动同步状态
./scripts/sync-status.sh --force

# 重置当前任务状态
rm -f automation-state/current-task.json
```

### 权限问题处理

**常见权限错误：**
- PAT过期或权限不足
- 项目访问被拒绝
- Secret配置错误

**解决方案：**
```bash
# 重新生成PAT
# 1. 访问 GitHub Settings > Developer settings > Personal access tokens
# 2. 生成新token，确保包含repo和project权限
# 3. 更新Secret: gh secret set CHAIGUANJIA_PAT --body="<new-token>"

# 验证新配置
gh secret list
gh api graphql -f query='query { viewer { login } }'
```

---

*本规则文档基于《GitHub Projects自动化系统完整指南.md》制定*
*版本：v1.0 | 更新：2025-08-02 | 适用：柴管家项目GitHub自动化操作*
