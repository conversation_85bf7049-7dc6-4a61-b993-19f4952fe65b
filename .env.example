# 柴管家环境配置模板
# 复制此文件为 .env 并填入实际配置值

# 应用配置
APP_NAME=chaiguanjia
APP_VERSION=1.0.0
APP_ENV=development
DEBUG=true
SECRET_KEY=your-secret-key-here

# 服务器配置
HOST=0.0.0.0
PORT=8000
FRONTEND_URL=http://localhost:3000

# 数据库配置
# PostgreSQL 主数据库
DATABASE_URL=postgresql://chaiguanjia_user:password@localhost:5432/chaiguanjia_db
DB_HOST=localhost
DB_PORT=5432
DB_NAME=chaiguanjia_db
DB_USER=chaiguanjia_user
DB_PASSWORD=password

# ChromaDB 向量数据库
CHROMA_DB_PATH=./chroma_db
CHROMA_DB_HOST=localhost
CHROMA_DB_PORT=8001

# Redis 缓存 (可选)
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# RabbitMQ 消息队列
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/

# AI 服务配置
# OpenAI API
OPENAI_API_KEY=your-openai-api-key
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# 或者使用其他AI服务
# AZURE_OPENAI_API_KEY=your-azure-openai-key
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
# AZURE_OPENAI_API_VERSION=2023-05-15

# 第三方平台配置
# 闲鱼连接器配置
XIANYU_APP_ID=your-xianyu-app-id
XIANYU_APP_SECRET=your-xianyu-app-secret
XIANYU_REDIRECT_URI=http://localhost:8000/api/v1/channels/xianyu/callback

# 微信连接器配置
WECHAT_APP_ID=your-wechat-app-id
WECHAT_APP_SECRET=your-wechat-app-secret

# 抖音连接器配置
DOUYIN_APP_ID=your-douyin-app-id
DOUYIN_APP_SECRET=your-douyin-app-secret

# JWT 配置
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/chaiguanjia.log

# CORS 配置
CORS_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE", "OPTIONS"]
CORS_ALLOW_HEADERS=["*"]

# 文件上传配置
UPLOAD_MAX_SIZE=10485760  # 10MB
UPLOAD_ALLOWED_EXTENSIONS=["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx"]
UPLOAD_PATH=uploads/

# 监控配置
SENTRY_DSN=your-sentry-dsn
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090

# 开发工具配置
ENABLE_SWAGGER=true
ENABLE_REDOC=true
ENABLE_DEBUG_TOOLBAR=true

# 测试配置
TEST_DATABASE_URL=postgresql://test_user:test_password@localhost:5432/test_chaiguanjia_db
