# 柴管家 CI/CD 流水线
# 持续集成和持续部署工作流

name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  PYTHON_VERSION: '3.11'
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r src/Host/requirements.txt
        pip install black isort flake8 mypy pytest-cov
    
    - name: 代码格式检查 (Black)
      run: black --check --diff chaiguanjia/ src/Host/
    
    - name: 导入排序检查 (isort)
      run: isort --check-only --diff chaiguanjia/ src/Host/
    
    - name: 代码风格检查 (flake8)
      run: flake8 chaiguanjia/ src/Host/ --max-line-length=88 --extend-ignore=E203,W503
    
    - name: 类型检查 (mypy)
      run: mypy chaiguanjia/ --ignore-missing-imports
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: 安装前端依赖
      working-directory: frontend
      run: npm ci
    
    - name: 前端代码检查 (ESLint)
      working-directory: frontend
      run: npm run lint
    
    - name: 前端类型检查 (TypeScript)
      working-directory: frontend
      run: npm run type-check

  # 后端测试
  backend-tests:
    name: 后端测试
    runs-on: ubuntu-latest
    needs: code-quality
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: chaiguanjia_test
          POSTGRES_USER: chaiguanjia
          POSTGRES_PASSWORD: chaiguanjia123
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
      
      rabbitmq:
        image: rabbitmq:3.12
        env:
          RABBITMQ_DEFAULT_USER: chaiguanjia
          RABBITMQ_DEFAULT_PASS: chaiguanjia123
        options: >-
          --health-cmd "rabbitmq-diagnostics ping"
          --health-interval 30s
          --health-timeout 10s
          --health-retries 5
        ports:
          - 5672:5672
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r src/Host/requirements.txt
        pip install pytest pytest-cov pytest-asyncio
    
    - name: 运行后端测试
      env:
        DATABASE_URL: postgresql+asyncpg://chaiguanjia:chaiguanjia123@localhost:5432/chaiguanjia_test
        REDIS_URL: redis://localhost:6379/0
        RABBITMQ_URL: amqp://chaiguanjia:chaiguanjia123@localhost:5672/
        SECRET_KEY: test-secret-key
        APP_ENV: testing
      run: |
        pytest tests/ -v --cov=chaiguanjia --cov-report=xml --cov-report=html
    
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: backend
        name: backend-coverage

  # 前端测试
  frontend-tests:
    name: 前端测试
    runs-on: ubuntu-latest
    needs: code-quality
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: 安装依赖
      working-directory: frontend
      run: npm ci
    
    - name: 运行前端测试
      working-directory: frontend
      run: npm run test:coverage
    
    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v3
      with:
        file: ./frontend/coverage/lcov.info
        flags: frontend
        name: frontend-coverage

  # 构建镜像
  build-images:
    name: 构建Docker镜像
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    if: github.event_name == 'push'
    
    permissions:
      contents: read
      packages: write
    
    strategy:
      matrix:
        component: [backend, frontend]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: 登录容器注册表
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 提取元数据
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.component }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: 构建并推送镜像
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.${{ matrix.component }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    needs: build-images
    if: github.event_name == 'push'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 运行Trivy漏洞扫描
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
    
    - name: 上传Trivy扫描结果
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

  # 部署到开发环境
  deploy-dev:
    name: 部署到开发环境
    runs-on: ubuntu-latest
    needs: [build-images, security-scan]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: development
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 部署到开发环境
      run: |
        echo "部署到开发环境..."
        # 这里可以添加实际的部署脚本
        # 例如：kubectl apply -f k8s/dev/ 或者 docker-compose up -d
    
    - name: 运行部署后测试
      run: |
        echo "运行部署后测试..."
        # 这里可以添加部署后的健康检查和集成测试

  # 部署到生产环境
  deploy-prod:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [build-images, security-scan]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 部署到生产环境
      run: |
        echo "部署到生产环境..."
        # 这里可以添加实际的生产部署脚本
    
    - name: 运行生产环境验证
      run: |
        echo "运行生产环境验证..."
        # 这里可以添加生产环境的健康检查
