# 代码质量检查工作流
# 专门用于代码质量和安全检查

name: Code Quality

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # 每天凌晨2点运行
    - cron: '0 2 * * *'

jobs:
  # Python代码质量检查
  python-quality:
    name: Python代码质量
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
    
    - name: 安装依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r src/Host/requirements.txt
        pip install black isort flake8 mypy bandit safety
    
    - name: 代码格式检查 (Black)
      run: |
        black --check --diff chaiguanjia/ src/Host/
        echo "Black检查完成"
    
    - name: 导入排序检查 (isort)
      run: |
        isort --check-only --diff chaiguanjia/ src/Host/
        echo "isort检查完成"
    
    - name: 代码风格检查 (flake8)
      run: |
        flake8 chaiguanjia/ src/Host/ \
          --max-line-length=88 \
          --extend-ignore=E203,W503 \
          --statistics \
          --count
    
    - name: 类型检查 (mypy)
      run: |
        mypy chaiguanjia/ \
          --ignore-missing-imports \
          --show-error-codes \
          --pretty
    
    - name: 安全检查 (bandit)
      run: |
        bandit -r chaiguanjia/ src/Host/ \
          -f json \
          -o bandit-report.json
        bandit -r chaiguanjia/ src/Host/ \
          -f txt
    
    - name: 依赖安全检查 (safety)
      run: |
        safety check --json --output safety-report.json || true
        safety check
    
    - name: 上传安全报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: python-security-reports
        path: |
          bandit-report.json
          safety-report.json

  # JavaScript/TypeScript代码质量检查
  javascript-quality:
    name: JavaScript代码质量
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: 安装依赖
      working-directory: frontend
      run: npm ci
    
    - name: ESLint检查
      working-directory: frontend
      run: |
        npm run lint -- --format=json --output-file=eslint-report.json || true
        npm run lint
    
    - name: Prettier格式检查
      working-directory: frontend
      run: npm run format:check
    
    - name: TypeScript类型检查
      working-directory: frontend
      run: npm run type-check
    
    - name: 依赖审计
      working-directory: frontend
      run: |
        npm audit --audit-level=moderate --json > npm-audit.json || true
        npm audit --audit-level=moderate
    
    - name: 上传前端质量报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: frontend-quality-reports
        path: |
          frontend/eslint-report.json
          frontend/npm-audit.json

  # 代码复杂度分析
  complexity-analysis:
    name: 代码复杂度分析
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # 获取完整历史用于分析
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: 安装分析工具
      run: |
        pip install radon xenon
    
    - name: 圈复杂度分析
      run: |
        echo "=== 圈复杂度分析 ==="
        radon cc chaiguanjia/ src/Host/ -a -s
        echo ""
        echo "=== 维护性指数 ==="
        radon mi chaiguanjia/ src/Host/ -s
        echo ""
        echo "=== 原始指标 ==="
        radon raw chaiguanjia/ src/Host/ -s
    
    - name: 复杂度检查
      run: |
        xenon --max-absolute B --max-modules A --max-average A chaiguanjia/ src/Host/

  # SonarCloud分析
  sonarcloud:
    name: SonarCloud分析
    runs-on: ubuntu-latest
    if: github.event_name != 'schedule'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0  # SonarCloud需要完整历史
    
    - name: SonarCloud扫描
      uses: SonarSource/sonarcloud-github-action@master
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # 许可证检查
  license-check:
    name: 许可证检查
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: 安装许可证检查工具
      run: pip install pip-licenses
    
    - name: Python依赖许可证检查
      run: |
        pip install -r src/Host/requirements.txt
        pip-licenses --format=json --output-file=python-licenses.json
        pip-licenses --format=table
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: 安装前端依赖
      working-directory: frontend
      run: npm ci
    
    - name: 前端依赖许可证检查
      working-directory: frontend
      run: |
        npx license-checker --json --out ../frontend-licenses.json
        npx license-checker
    
    - name: 上传许可证报告
      uses: actions/upload-artifact@v3
      with:
        name: license-reports
        path: |
          python-licenses.json
          frontend-licenses.json

  # 代码质量总结
  quality-summary:
    name: 代码质量总结
    runs-on: ubuntu-latest
    needs: [python-quality, javascript-quality, complexity-analysis, license-check]
    if: always()
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 下载所有报告
      uses: actions/download-artifact@v3
    
    - name: 生成质量报告
      run: |
        echo "# 代码质量检查报告" > quality-report.md
        echo "" >> quality-report.md
        echo "## 检查结果" >> quality-report.md
        echo "" >> quality-report.md
        
        if [ "${{ needs.python-quality.result }}" == "success" ]; then
          echo "✅ Python代码质量检查通过" >> quality-report.md
        else
          echo "❌ Python代码质量检查失败" >> quality-report.md
        fi
        
        if [ "${{ needs.javascript-quality.result }}" == "success" ]; then
          echo "✅ JavaScript代码质量检查通过" >> quality-report.md
        else
          echo "❌ JavaScript代码质量检查失败" >> quality-report.md
        fi
        
        if [ "${{ needs.complexity-analysis.result }}" == "success" ]; then
          echo "✅ 代码复杂度分析通过" >> quality-report.md
        else
          echo "❌ 代码复杂度分析失败" >> quality-report.md
        fi
        
        if [ "${{ needs.license-check.result }}" == "success" ]; then
          echo "✅ 许可证检查通过" >> quality-report.md
        else
          echo "❌ 许可证检查失败" >> quality-report.md
        fi
        
        echo "" >> quality-report.md
        echo "## 详细信息" >> quality-report.md
        echo "请查看各个job的详细日志获取更多信息。" >> quality-report.md
        
        cat quality-report.md
    
    - name: 上传质量报告
      uses: actions/upload-artifact@v3
      with:
        name: quality-summary
        path: quality-report.md
