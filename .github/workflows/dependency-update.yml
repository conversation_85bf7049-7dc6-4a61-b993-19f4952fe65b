# 依赖更新工作流
# 自动检查和更新项目依赖

name: Dependency Update

on:
  schedule:
    # 每周一凌晨3点运行
    - cron: '0 3 * * 1'
  workflow_dispatch:

jobs:
  # Python依赖更新
  update-python-deps:
    name: 更新Python依赖
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: 安装pip-tools
      run: pip install pip-tools
    
    - name: 更新requirements.txt
      run: |
        cd src/Host
        pip-compile --upgrade requirements.in
    
    - name: 检查安全漏洞
      run: |
        pip install safety
        safety check -r src/Host/requirements.txt --json --output safety-report.json || true
    
    - name: 创建Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: "chore(deps): 更新Python依赖"
        title: "🔄 自动更新Python依赖"
        body: |
          ## 依赖更新
          
          这是一个自动生成的PR，用于更新Python依赖包。
          
          ### 更改内容
          - 更新了 `requirements.txt` 中的依赖版本
          
          ### 安全检查
          请查看安全扫描报告确保没有已知漏洞。
          
          ### 测试
          请确保所有测试通过后再合并此PR。
        branch: auto-update-python-deps
        delete-branch: true

  # Node.js依赖更新
  update-node-deps:
    name: 更新Node.js依赖
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: 更新依赖
      working-directory: frontend
      run: |
        npm update
        npm audit fix --audit-level=moderate || true
    
    - name: 运行安全审计
      working-directory: frontend
      run: |
        npm audit --json > npm-audit-report.json || true
    
    - name: 创建Pull Request
      uses: peter-evans/create-pull-request@v5
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: "chore(deps): 更新Node.js依赖"
        title: "🔄 自动更新Node.js依赖"
        body: |
          ## 依赖更新
          
          这是一个自动生成的PR，用于更新Node.js依赖包。
          
          ### 更改内容
          - 更新了 `package.json` 和 `package-lock.json` 中的依赖版本
          - 修复了已知的安全漏洞
          
          ### 安全检查
          请查看npm audit报告确保没有严重的安全问题。
          
          ### 测试
          请确保所有测试通过后再合并此PR。
        branch: auto-update-node-deps
        delete-branch: true

  # Docker基础镜像更新检查
  check-docker-updates:
    name: 检查Docker镜像更新
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 检查基础镜像更新
      run: |
        echo "检查Docker基础镜像更新..."
        
        # 检查Python镜像
        CURRENT_PYTHON=$(grep "FROM python:" Dockerfile.backend | head -1 | cut -d: -f2)
        echo "当前Python镜像版本: $CURRENT_PYTHON"
        
        # 检查Node镜像
        CURRENT_NODE=$(grep "FROM node:" Dockerfile.frontend | head -1 | cut -d: -f2 | cut -d' ' -f1)
        echo "当前Node镜像版本: $CURRENT_NODE"
        
        # 检查Nginx镜像
        CURRENT_NGINX=$(grep "FROM nginx:" Dockerfile.frontend | head -1 | cut -d: -f2)
        echo "当前Nginx镜像版本: $CURRENT_NGINX"
        
        # 这里可以添加更复杂的版本比较逻辑
        echo "Docker镜像检查完成"

  # 依赖漏洞扫描
  vulnerability-scan:
    name: 依赖漏洞扫描
    runs-on: ubuntu-latest
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Python依赖漏洞扫描
      run: |
        pip install safety
        pip install -r src/Host/requirements.txt
        safety check --json --output python-vulnerabilities.json || true
        safety check
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: 安装前端依赖
      working-directory: frontend
      run: npm ci
    
    - name: Node.js依赖漏洞扫描
      working-directory: frontend
      run: |
        npm audit --json > ../node-vulnerabilities.json || true
        npm audit
    
    - name: 上传漏洞报告
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: vulnerability-reports
        path: |
          python-vulnerabilities.json
          node-vulnerabilities.json
    
    - name: 创建Issue（如果发现漏洞）
      if: failure()
      uses: actions/github-script@v6
      with:
        script: |
          const title = '🚨 发现依赖安全漏洞';
          const body = `
          ## 安全漏洞报告
          
          在依赖扫描中发现了安全漏洞，请及时处理。
          
          ### 扫描时间
          ${new Date().toISOString()}
          
          ### 详细信息
          请查看工作流运行日志获取详细的漏洞信息。
          
          ### 建议操作
          1. 查看具体的漏洞详情
          2. 更新相关依赖到安全版本
          3. 运行测试确保更新不会破坏功能
          4. 关闭此Issue
          
          ---
          *此Issue由自动化工作流创建*
          `;
          
          // 检查是否已存在相同的Issue
          const issues = await github.rest.issues.listForRepo({
            owner: context.repo.owner,
            repo: context.repo.repo,
            state: 'open',
            labels: ['security', 'dependencies']
          });
          
          const existingIssue = issues.data.find(issue => issue.title === title);
          
          if (!existingIssue) {
            await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: title,
              body: body,
              labels: ['security', 'dependencies', 'automated']
            });
          }

  # 依赖更新总结
  dependency-summary:
    name: 依赖更新总结
    runs-on: ubuntu-latest
    needs: [update-python-deps, update-node-deps, check-docker-updates, vulnerability-scan]
    if: always()
    
    steps:
    - name: 生成总结报告
      run: |
        echo "# 依赖更新总结报告" > summary.md
        echo "" >> summary.md
        echo "## 更新状态" >> summary.md
        echo "" >> summary.md
        
        if [ "${{ needs.update-python-deps.result }}" == "success" ]; then
          echo "✅ Python依赖更新完成" >> summary.md
        else
          echo "❌ Python依赖更新失败" >> summary.md
        fi
        
        if [ "${{ needs.update-node-deps.result }}" == "success" ]; then
          echo "✅ Node.js依赖更新完成" >> summary.md
        else
          echo "❌ Node.js依赖更新失败" >> summary.md
        fi
        
        if [ "${{ needs.check-docker-updates.result }}" == "success" ]; then
          echo "✅ Docker镜像检查完成" >> summary.md
        else
          echo "❌ Docker镜像检查失败" >> summary.md
        fi
        
        if [ "${{ needs.vulnerability-scan.result }}" == "success" ]; then
          echo "✅ 漏洞扫描通过" >> summary.md
        else
          echo "⚠️ 发现安全漏洞，请及时处理" >> summary.md
        fi
        
        echo "" >> summary.md
        echo "## 后续操作" >> summary.md
        echo "1. 检查自动创建的Pull Request" >> summary.md
        echo "2. 运行测试确保更新不会破坏功能" >> summary.md
        echo "3. 合并通过测试的更新" >> summary.md
        echo "4. 处理任何发现的安全漏洞" >> summary.md
        
        cat summary.md
    
    - name: 上传总结报告
      uses: actions/upload-artifact@v3
      with:
        name: dependency-summary
        path: summary.md
