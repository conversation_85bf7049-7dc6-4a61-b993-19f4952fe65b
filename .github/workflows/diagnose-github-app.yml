name: 🔍 GitHub App诊断

on:
  workflow_dispatch:
    inputs:
      project_number:
        description: '要测试的项目编号'
        required: false
        default: '1'
        type: string

jobs:
  diagnose:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置访问令牌
        id: app-token
        run: |
          # 优先使用Personal Access Token，如果不存在则使用GitHub App
          if [ -n "${{ secrets.CHAIGUANJIA_PAT }}" ]; then
            echo "token=${{ secrets.CHAIGUANJIA_PAT }}" >> $GITHUB_OUTPUT
            echo "✅ 使用Personal Access Token"
          else
            echo "⚠️ 未找到CHAIGUANJIA_PAT，请配置Personal Access Token"
            echo "📖 配置指南: https://github.com/settings/tokens"
            exit 1
          fi

      - name: 运行GitHub App诊断
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
          PROJECT_NUMBER: ${{ github.event.inputs.project_number }}
          PROJECT_TYPE: "user"  # 设置为个人项目
        run: |
          echo "🔍 开始GitHub App诊断..."
          echo "项目编号: $PROJECT_NUMBER"
          echo "项目类型: $PROJECT_TYPE"
          echo "账户: Amoresdk"
          echo ""

          # 运行诊断脚本
          bash scripts/test-github-app.sh
          
      - name: 诊断结果摘要
        if: always()
        run: |
          echo "## 🔍 GitHub App诊断完成" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "请查看上面的日志输出以了解详细的诊断结果。" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 常见问题解决方案:" >> $GITHUB_STEP_SUMMARY
          echo "1. **项目不存在**: 请确认项目编号是否正确" >> $GITHUB_STEP_SUMMARY
          echo "2. **权限不足**: 请确认GitHub App是否有Projects权限" >> $GITHUB_STEP_SUMMARY
          echo "3. **字段缺失**: 请确认项目是否有Status字段" >> $GITHUB_STEP_SUMMARY
