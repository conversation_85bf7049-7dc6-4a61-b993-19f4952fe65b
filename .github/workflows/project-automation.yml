name: 柴管家项目看板自动化

on:
  issues:
    types: [opened, closed]
  pull_request:
    types: [opened, closed, merged]

env:
  PROJECT_OWNER: Amoresdk
  PROJECT_NUMBER: 1
  PROJECT_NAME: "柴管家开发任务看板"

jobs:
  sync-to-project:
    name: 同步到项目看板
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置访问令牌
        id: app-token
        run: |
          if [ -n "${{ secrets.CHAIGUANJIA_PAT }}" ]; then
            echo "token=${{ secrets.CHAIGUANJIA_PAT }}" >> $GITHUB_OUTPUT
            echo "✅ 使用Personal Access Token进行认证"
          else
            echo "❌ 错误：未找到CHAIGUANJIA_PAT Secret"
            echo "请在仓库设置中配置Personal Access Token"
            exit 1
          fi

      - name: 获取项目配置
        id: project-config
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
        run: |
          echo "🔍 查询个人GitHub Projects配置..."

          # 查询个人项目信息
          project_data=$(gh api graphql -f query='
            query($owner: String!, $number: Int!) {
              user(login: $owner) {
                projectV2(number: $number) {
                  id
                  title
                  fields(first: 20) {
                    nodes {
                      ... on ProjectV2Field {
                        id
                        name
                      }
                      ... on ProjectV2SingleSelectField {
                        id
                        name
                        options {
                          id
                          name
                        }
                      }
                    }
                  }
                }
              }
            }' -f owner=$PROJECT_OWNER -F number=$PROJECT_NUMBER)

          # 检查查询结果
          if [ $? -ne 0 ]; then
            echo "❌ 错误：无法查询项目信息"
            exit 1
          fi

          # 提取项目ID
          project_id=$(echo "$project_data" | jq -r '.data.user.projectV2.id')
          if [ "$project_id" = "null" ] || [ -z "$project_id" ]; then
            echo "❌ 错误：未找到项目 #$PROJECT_NUMBER"
            echo "请确认项目编号和访问权限"
            exit 1
          fi

          echo "✅ 找到项目: $project_id"
          echo "project_id=$project_id" >> $GITHUB_OUTPUT

          # 查找Status字段和选项
          status_field_id=$(echo "$project_data" | jq -r '.data.user.projectV2.fields.nodes[] | select(.name == "Status") | .id')
          todo_option_id=$(echo "$project_data" | jq -r '.data.user.projectV2.fields.nodes[] | select(.name == "Status") | .options[] | select(.name == "Todo") | .id')
          done_option_id=$(echo "$project_data" | jq -r '.data.user.projectV2.fields.nodes[] | select(.name == "Status") | .options[] | select(.name == "Done") | .id')

          echo "status_field_id=$status_field_id" >> $GITHUB_OUTPUT
          echo "todo_option_id=$todo_option_id" >> $GITHUB_OUTPUT
          echo "done_option_id=$done_option_id" >> $GITHUB_OUTPUT

          echo "📋 项目字段配置:"
          echo "  - Status字段ID: $status_field_id"
          echo "  - Todo选项ID: $todo_option_id"
          echo "  - Done选项ID: $done_option_id"

      - name: 处理Issue创建事件
        if: github.event.action == 'opened' && github.event.issue
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
          PROJECT_ID: ${{ steps.project-config.outputs.project_id }}
          STATUS_FIELD_ID: ${{ steps.project-config.outputs.status_field_id }}
          TODO_OPTION_ID: ${{ steps.project-config.outputs.todo_option_id }}
        run: |
          echo "📝 处理Issue创建事件: #${{ github.event.issue.number }}"
          echo "Issue标题: ${{ github.event.issue.title }}"

          # 检查Issue是否已在项目中
          echo "🔍 检查Issue是否已在项目中..."
          existing_item=$(gh api graphql -f query='
            query($owner: String!, $number: Int!) {
              user(login: $owner) {
                projectV2(number: $number) {
                  items(first: 100) {
                    nodes {
                      id
                      content {
                        ... on Issue {
                          number
                        }
                      }
                    }
                  }
                }
              }
            }' -f owner=$PROJECT_OWNER -F number=$PROJECT_NUMBER | \
            jq -r '.data.user.projectV2.items.nodes[]? | select(.content.number == ${{ github.event.issue.number }}) | .id')

          if [ -n "$existing_item" ] && [ "$existing_item" != "null" ]; then
            echo "ℹ️ Issue #${{ github.event.issue.number }} 已存在于项目中 (ID: $existing_item)"
            exit 0
          fi

          echo "➕ 将Issue添加到项目..."

          # 添加Issue到项目
          add_result=$(gh api graphql -f query='
            mutation($projectId: ID!, $contentId: ID!) {
              addProjectV2ItemById(input: {
                projectId: $projectId
                contentId: $contentId
              }) {
                item {
                  id
                }
              }
            }' -f projectId="$PROJECT_ID" -f contentId="${{ github.event.issue.node_id }}")

          if [ $? -ne 0 ]; then
            echo "❌ 错误：无法添加Issue到项目"
            exit 1
          fi

          item_id=$(echo "$add_result" | jq -r '.data.addProjectV2ItemById.item.id')

          if [ "$item_id" = "null" ] || [ -z "$item_id" ]; then
            echo "❌ 错误：无法获取项目条目ID"
            exit 1
          fi

          echo "✅ Issue已添加到项目 (条目ID: $item_id)"

          # 设置状态为Todo（如果有Status字段）
          if [ -n "$STATUS_FIELD_ID" ] && [ "$STATUS_FIELD_ID" != "null" ] && [ -n "$TODO_OPTION_ID" ] && [ "$TODO_OPTION_ID" != "null" ]; then
            echo "🔄 设置Issue状态为'待办'..."

            status_result=$(gh api graphql -f query='
              mutation($projectId: ID!, $itemId: ID!, $fieldId: ID!, $optionId: String!) {
                updateProjectV2ItemFieldValue(input: {
                  projectId: $projectId
                  itemId: $itemId
                  fieldId: $fieldId
                  value: {
                    singleSelectOptionId: $optionId
                  }
                }) {
                  projectV2Item {
                    id
                  }
                }
              }' -f projectId="$PROJECT_ID" -f itemId="$item_id" -f fieldId="$STATUS_FIELD_ID" -f optionId="$TODO_OPTION_ID")

            if [ $? -eq 0 ]; then
              echo "✅ Issue状态已设置为'待办'"
            else
              echo "⚠️ 警告：无法设置Issue状态，但Issue已成功添加到项目"
            fi
          else
            echo "ℹ️ 未找到Status字段配置，跳过状态设置"
          fi

          echo "🎉 Issue #${{ github.event.issue.number }} 已成功同步到项目看板"

      - name: 处理Issue关闭事件
        if: github.event.action == 'closed' && github.event.issue
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
          PROJECT_ID: ${{ steps.project-config.outputs.project_id }}
          STATUS_FIELD_ID: ${{ steps.project-config.outputs.status_field_id }}
          DONE_OPTION_ID: ${{ steps.project-config.outputs.done_option_id }}
        run: |
          echo "🔒 处理Issue关闭事件: #${{ github.event.issue.number }}"
          echo "Issue标题: ${{ github.event.issue.title }}"

          # 查找Issue在项目中的条目ID
          echo "🔍 查找Issue在项目中的条目..."
          item_id=$(gh api graphql -f query='
            query($owner: String!, $number: Int!) {
              user(login: $owner) {
                projectV2(number: $number) {
                  items(first: 100) {
                    nodes {
                      id
                      content {
                        ... on Issue {
                          number
                        }
                      }
                    }
                  }
                }
              }
            }' -f owner=$PROJECT_OWNER -F number=$PROJECT_NUMBER | \
            jq -r '.data.user.projectV2.items.nodes[]? | select(.content.number == ${{ github.event.issue.number }}) | .id')

          if [ -z "$item_id" ] || [ "$item_id" = "null" ]; then
            echo "ℹ️ Issue #${{ github.event.issue.number }} 不在项目中，无需更新状态"
            exit 0
          fi

          echo "✅ 找到项目条目ID: $item_id"

          # 更新状态为Done（如果有Status字段）
          if [ -n "$STATUS_FIELD_ID" ] && [ "$STATUS_FIELD_ID" != "null" ] && [ -n "$DONE_OPTION_ID" ] && [ "$DONE_OPTION_ID" != "null" ]; then
            echo "🔄 设置Issue状态为'已完成'..."

            status_result=$(gh api graphql -f query='
              mutation($projectId: ID!, $itemId: ID!, $fieldId: ID!, $optionId: String!) {
                updateProjectV2ItemFieldValue(input: {
                  projectId: $projectId
                  itemId: $itemId
                  fieldId: $fieldId
                  value: {
                    singleSelectOptionId: $optionId
                  }
                }) {
                  projectV2Item {
                    id
                  }
                }
              }' -f projectId="$PROJECT_ID" -f itemId="$item_id" -f fieldId="$STATUS_FIELD_ID" -f optionId="$DONE_OPTION_ID")

            if [ $? -eq 0 ]; then
              echo "✅ Issue状态已更新为'已完成'"
            else
              echo "❌ 错误：无法更新Issue状态"
              exit 1
            fi
          else
            echo "ℹ️ 未找到Status字段配置，跳过状态更新"
          fi

          echo "🎉 Issue #${{ github.event.issue.number }} 状态已同步为'已完成'"

      - name: 处理PR事件
        if: github.event.pull_request
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
        run: |
          echo "🔀 处理Pull Request事件: #${{ github.event.pull_request.number }}"
          echo "PR标题: ${{ github.event.pull_request.title }}"
          echo "事件类型: ${{ github.event.action }}"

          # 目前仅记录PR事件，后续可根据需要添加PR到项目的逻辑
          echo "ℹ️ PR事件已记录，如需要可在此添加PR同步逻辑"

      - name: 记录操作日志
        if: always()
        run: |
          echo "📊 操作完成总结:"
          echo "  - 仓库: ${{ github.repository }}"
          echo "  - 事件类型: ${{ github.event_name }}"
          echo "  - 触发动作: ${{ github.event.action }}"
          echo "  - 项目: $PROJECT_NAME (#$PROJECT_NUMBER)"
          echo "  - 时间: $(date -u '+%Y-%m-%d %H:%M:%S UTC')"

          if [ "${{ github.event.issue }}" != "" ]; then
            echo "  - Issue: #${{ github.event.issue.number }} - ${{ github.event.issue.title }}"
          fi

          if [ "${{ github.event.pull_request }}" != "" ]; then
            echo "  - PR: #${{ github.event.pull_request.number }} - ${{ github.event.pull_request.title }}"
          fi
