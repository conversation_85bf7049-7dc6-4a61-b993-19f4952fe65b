# 发布工作流
# 自动化版本发布流程

name: Release

on:
  push:
    tags:
      - 'v*.*.*'
  workflow_dispatch:
    inputs:
      version:
        description: '发布版本号 (例如: v1.0.0)'
        required: true
        type: string

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # 验证发布条件
  validate-release:
    name: 验证发布条件
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.version.outputs.version }}
      is_prerelease: ${{ steps.version.outputs.is_prerelease }}
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 获取版本信息
      id: version
      run: |
        if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
          VERSION="${{ github.event.inputs.version }}"
        else
          VERSION=${GITHUB_REF#refs/tags/}
        fi
        
        echo "version=$VERSION" >> $GITHUB_OUTPUT
        
        # 检查是否为预发布版本
        if [[ $VERSION =~ -[a-zA-Z] ]]; then
          echo "is_prerelease=true" >> $GITHUB_OUTPUT
        else
          echo "is_prerelease=false" >> $GITHUB_OUTPUT
        fi
        
        echo "发布版本: $VERSION"
        echo "预发布: $([ "${{ steps.version.outputs.is_prerelease }}" == "true" ] && echo "是" || echo "否")"
    
    - name: 验证版本格式
      run: |
        VERSION="${{ steps.version.outputs.version }}"
        if [[ ! $VERSION =~ ^v[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+)?$ ]]; then
          echo "错误: 版本格式不正确。应为 vX.Y.Z 或 vX.Y.Z-suffix"
          exit 1
        fi
    
    - name: 检查是否为新版本
      run: |
        VERSION="${{ steps.version.outputs.version }}"
        if git tag -l | grep -q "^$VERSION$"; then
          echo "错误: 版本 $VERSION 已存在"
          exit 1
        fi

  # 运行完整测试
  run-tests:
    name: 运行完整测试
    runs-on: ubuntu-latest
    needs: validate-release
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_DB: chaiguanjia_test
          POSTGRES_USER: chaiguanjia
          POSTGRES_PASSWORD: chaiguanjia123
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Python环境
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
        cache: 'pip'
    
    - name: 安装Python依赖
      run: |
        python -m pip install --upgrade pip
        pip install -r src/Host/requirements.txt
        pip install pytest pytest-cov
    
    - name: 运行后端测试
      env:
        DATABASE_URL: postgresql+asyncpg://chaiguanjia:chaiguanjia123@localhost:5432/chaiguanjia_test
        REDIS_URL: redis://localhost:6379/0
        SECRET_KEY: test-secret-key
        APP_ENV: testing
      run: pytest tests/ -v --cov=chaiguanjia
    
    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json
    
    - name: 安装前端依赖
      working-directory: frontend
      run: npm ci
    
    - name: 运行前端测试
      working-directory: frontend
      run: npm test

  # 构建发布镜像
  build-release-images:
    name: 构建发布镜像
    runs-on: ubuntu-latest
    needs: [validate-release, run-tests]
    permissions:
      contents: read
      packages: write
    
    strategy:
      matrix:
        component: [backend, frontend]
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: 登录容器注册表
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: 提取元数据
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-${{ matrix.component }}
        tags: |
          type=semver,pattern={{version}},value=${{ needs.validate-release.outputs.version }}
          type=semver,pattern={{major}}.{{minor}},value=${{ needs.validate-release.outputs.version }}
          type=semver,pattern={{major}},value=${{ needs.validate-release.outputs.version }}
          type=raw,value=latest,enable={{is_default_branch}}
    
    - name: 构建并推送发布镜像
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.${{ matrix.component }}
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
        platforms: linux/amd64,linux/arm64

  # 生成变更日志
  generate-changelog:
    name: 生成变更日志
    runs-on: ubuntu-latest
    needs: validate-release
    outputs:
      changelog: ${{ steps.changelog.outputs.changelog }}
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: 生成变更日志
      id: changelog
      run: |
        VERSION="${{ needs.validate-release.outputs.version }}"
        PREV_TAG=$(git tag -l --sort=-version:refname | head -n 1)
        
        if [ -z "$PREV_TAG" ]; then
          PREV_TAG=$(git rev-list --max-parents=0 HEAD)
        fi
        
        echo "从 $PREV_TAG 到 $VERSION 的变更:"
        
        # 生成变更日志
        CHANGELOG=$(git log --pretty=format:"- %s (%h)" $PREV_TAG..HEAD)
        
        # 保存到文件
        echo "# 变更日志 - $VERSION" > CHANGELOG.md
        echo "" >> CHANGELOG.md
        echo "## 新功能" >> CHANGELOG.md
        echo "$CHANGELOG" | grep -i "feat\|add\|new" || echo "- 无" >> CHANGELOG.md
        echo "" >> CHANGELOG.md
        echo "## 修复" >> CHANGELOG.md
        echo "$CHANGELOG" | grep -i "fix\|bug" || echo "- 无" >> CHANGELOG.md
        echo "" >> CHANGELOG.md
        echo "## 改进" >> CHANGELOG.md
        echo "$CHANGELOG" | grep -i "improve\|enhance\|update" || echo "- 无" >> CHANGELOG.md
        echo "" >> CHANGELOG.md
        echo "## 其他变更" >> CHANGELOG.md
        echo "$CHANGELOG" | grep -v -i "feat\|add\|new\|fix\|bug\|improve\|enhance\|update" || echo "- 无" >> CHANGELOG.md
        
        # 输出到GitHub
        {
          echo 'changelog<<EOF'
          cat CHANGELOG.md
          echo EOF
        } >> $GITHUB_OUTPUT
    
    - name: 上传变更日志
      uses: actions/upload-artifact@v3
      with:
        name: changelog
        path: CHANGELOG.md

  # 创建GitHub Release
  create-release:
    name: 创建GitHub Release
    runs-on: ubuntu-latest
    needs: [validate-release, run-tests, build-release-images, generate-changelog]
    permissions:
      contents: write
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 下载变更日志
      uses: actions/download-artifact@v3
      with:
        name: changelog
    
    - name: 创建Release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ needs.validate-release.outputs.version }}
        release_name: 柴管家 ${{ needs.validate-release.outputs.version }}
        body_path: CHANGELOG.md
        draft: false
        prerelease: ${{ needs.validate-release.outputs.is_prerelease }}

  # 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    needs: [validate-release, create-release]
    if: needs.validate-release.outputs.is_prerelease == 'false'
    environment: production
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
    
    - name: 部署到生产环境
      run: |
        echo "部署版本 ${{ needs.validate-release.outputs.version }} 到生产环境..."
        # 这里添加实际的生产部署脚本
        # 例如: kubectl set image deployment/chaiguanjia-backend backend=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-backend:${{ needs.validate-release.outputs.version }}
    
    - name: 验证部署
      run: |
        echo "验证生产环境部署..."
        # 这里添加部署验证脚本
        # 例如: curl -f https://api.chaiguanjia.com/health

  # 发布通知
  notify-release:
    name: 发布通知
    runs-on: ubuntu-latest
    needs: [validate-release, create-release, deploy-production]
    if: always() && needs.create-release.result == 'success'
    
    steps:
    - name: 发送发布通知
      run: |
        echo "🎉 柴管家 ${{ needs.validate-release.outputs.version }} 发布成功!"
        echo "📦 Docker镜像已推送到容器注册表"
        echo "🚀 GitHub Release已创建"
        
        if [ "${{ needs.validate-release.outputs.is_prerelease }}" == "false" ]; then
          echo "🌟 生产环境部署完成"
        else
          echo "🧪 这是一个预发布版本"
        fi
