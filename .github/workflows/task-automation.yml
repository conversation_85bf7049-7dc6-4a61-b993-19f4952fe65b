name: 柴管家任务自动化管理

on:
  # 手动触发
  workflow_dispatch:
    inputs:
      action:
        description: '操作类型'
        required: true
        default: 'start-next-task'
        type: choice
        options:
          - start-next-task
          - complete-task
          - sync-status
          - show-status
      issue_number:
        description: 'Issue编号 (仅用于complete-task)'
        required: false
        type: string
      dry_run:
        description: '预览模式 (不执行实际操作)'
        required: false
        default: false
        type: boolean

  # 定时触发 - 每小时检查一次
  schedule:
    - cron: '0 * * * *'

  # 事件触发
  issues:
    types: [closed]
  pull_request:
    types: [closed]

env:
  PROJECT_OWNER: Amoresdk  # 个人项目所有者
  PROJECT_NUMBER: 1  # 柴管家开发任务看板项目编号
  PROJECT_NAME: "柴管家开发任务看板"
  PROJECT_TYPE: "user"  # 个人项目类型

jobs:
  task_automation:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      issues: write
      pull-requests: write
      
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置访问令牌
        id: app-token
        run: |
          # 优先使用Personal Access Token，如果不存在则使用GitHub App
          if [ -n "${{ secrets.CHAIGUANJIA_PAT }}" ]; then
            echo "token=${{ secrets.CHAIGUANJIA_PAT }}" >> $GITHUB_OUTPUT
            echo "✅ 使用Personal Access Token"
          else
            echo "⚠️ 未找到CHAIGUANJIA_PAT，尝试使用GitHub App..."
            # 这里可以添加GitHub App token生成逻辑
            echo "❌ 请配置CHAIGUANJIA_PAT Secret"
            exit 1
          fi

      - name: 设置环境
        run: |
          # 创建必要的目录
          mkdir -p .github/automation-state
          mkdir -p scripts
          
          # 设置默认操作
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            echo "ACTION=${{ github.event.inputs.action }}" >> $GITHUB_ENV
            echo "ISSUE_NUMBER=${{ github.event.inputs.issue_number }}" >> $GITHUB_ENV
            echo "DRY_RUN=${{ github.event.inputs.dry_run }}" >> $GITHUB_ENV
          elif [ "${{ github.event_name }}" = "schedule" ]; then
            echo "ACTION=sync-status" >> $GITHUB_ENV
            echo "DRY_RUN=false" >> $GITHUB_ENV
          else
            echo "ACTION=sync-status" >> $GITHUB_ENV
            echo "DRY_RUN=false" >> $GITHUB_ENV
          fi

      - name: 获取项目配置
        id: project-config
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
        run: |
          echo "🔍 获取个人项目配置信息..."

          # 读取项目配置
          PROJECT_OWNER=$(jq -r '.project.owner' .github/project-config.json)
          PROJECT_NUMBER=$(jq -r '.project.number' .github/project-config.json)

          echo "项目所有者: $PROJECT_OWNER"
          echo "项目编号: $PROJECT_NUMBER"

          # 查询个人项目
          echo "🔍 查询个人GitHub Projects..."
          project_data=$(gh api graphql -f query='
            query($owner: String!, $number: Int!) {
              user(login: $owner) {
                projectV2(number: $number) {
                  id
                  title
                  fields(first: 20) {
                    nodes {
                      ... on ProjectV2Field {
                        id
                        name
                      }
                      ... on ProjectV2SingleSelectField {
                        id
                        name
                        options {
                          id
                          name
                        }
                      }
                    }
                  }
                }
              }
            }' -f owner=$PROJECT_OWNER -F number=$PROJECT_NUMBER)

          # 提取项目ID
          project_id=$(echo "$project_data" | jq -r '.data.user.projectV2.id')
          echo "PROJECT_ID=$project_id" >> $GITHUB_ENV

          # 提取状态字段信息
          status_field_id=$(echo "$project_data" | jq -r '.data.user.projectV2.fields.nodes[] | select(.name == "Status") | .id')
          echo "STATUS_FIELD_ID=$status_field_id" >> $GITHUB_ENV

          # 提取状态选项ID
          todo_id=$(echo "$project_data" | jq -r '.data.user.projectV2.fields.nodes[] | select(.name == "Status") | .options[] | select(.name == "Todo") | .id')
          in_progress_id=$(echo "$project_data" | jq -r '.data.user.projectV2.fields.nodes[] | select(.name == "Status") | .options[] | select(.name == "In Progress") | .id')
          done_id=$(echo "$project_data" | jq -r '.data.user.projectV2.fields.nodes[] | select(.name == "Status") | .options[] | select(.name == "Done") | .id')

          echo "TODO_OPTION_ID=$todo_id" >> $GITHUB_ENV
          echo "IN_PROGRESS_OPTION_ID=$in_progress_id" >> $GITHUB_ENV
          echo "DONE_OPTION_ID=$done_id" >> $GITHUB_ENV

          echo "✅ 个人项目配置获取完成"
          echo "项目ID: $project_id"
          echo "状态字段ID: $status_field_id"

      - name: 执行任务管理操作
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
        run: |
          case "$ACTION" in
            "start-next-task")
              echo "🚀 开始下一个任务..."
              bash scripts/start-next-task.sh
              ;;
            "complete-task")
              echo "✅ 完成任务 #$ISSUE_NUMBER..."
              bash scripts/complete-task.sh "$ISSUE_NUMBER"
              ;;
            "sync-status")
              echo "🔄 同步状态..."
              bash scripts/sync-status.sh
              ;;
            "show-status")
              echo "📊 显示当前状态..."
              bash scripts/show-status.sh
              ;;
            *)
              echo "❌ 未知操作: $ACTION"
              exit 1
              ;;
          esac

      - name: 更新状态文件
        if: env.DRY_RUN == 'false'
        run: |
          # 更新最后执行时间
          echo "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" > .github/automation-state/last-run.txt
          
          # 提交状态变更
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          
          if [ -n "$(git status --porcelain)" ]; then
            git add .github/automation-state/
            git commit -m "chore: 更新任务自动化状态 [skip ci]"
            git push
          fi

      - name: 发送通知
        if: failure()
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
        run: |
          echo "❌ 任务自动化执行失败，请检查日志"
          # 这里可以添加Slack、邮件或其他通知机制
