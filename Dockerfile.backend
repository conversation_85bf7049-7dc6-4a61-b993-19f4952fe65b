# 柴管家后端 Dockerfile
# 基于Python 3.11的官方镜像

FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY src/Host/requirements.txt /app/requirements.txt

# 安装Python依赖
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY chaiguanjia/ /app/chaiguanjia/
COPY src/Host/ /app/src/Host/
COPY scripts/ /app/scripts/
COPY config/ /app/config/

# 创建非root用户
RUN groupadd -r chaiguanjia && useradd -r -g chaiguanjia chaiguanjia
RUN chown -R chaiguanjia:chaiguanjia /app
USER chaiguanjia

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "src.Host.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
