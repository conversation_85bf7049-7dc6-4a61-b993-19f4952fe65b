# 柴管家前端 Dockerfile
# 多阶段构建：构建阶段 + 运行阶段

# 构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY frontend/package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY frontend/ .

# 构建应用
RUN npm run build

# 运行阶段
FROM nginx:alpine

# 复制自定义nginx配置
COPY config/nginx.conf /etc/nginx/nginx.conf

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 创建非root用户
RUN addgroup -g 1001 -S chaiguanjia && \
    adduser -S chaiguanjia -u 1001

# 设置权限
RUN chown -R chaiguanjia:chaiguanjia /usr/share/nginx/html && \
    chown -R chaiguanjia:chaiguanjia /var/cache/nginx && \
    chown -R chaiguanjia:chaiguanjia /var/log/nginx && \
    chown -R chaiguanjia:chaiguanjia /etc/nginx/conf.d

# 修改nginx配置以支持非root用户
RUN touch /var/run/nginx.pid && \
    chown -R chaiguanjia:chaiguanjia /var/run/nginx.pid

# 切换到非root用户
USER chaiguanjia

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1

# 暴露端口
EXPOSE 3000

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
