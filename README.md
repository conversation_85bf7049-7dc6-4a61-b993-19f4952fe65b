# 柴管家 (ChaiGuanJia) - 私域运营解决方案

## 项目概述

柴管家是为知识类、教培类个人IP运营者提供的一站式私域运营解决方案，通过聚合多平台消息和AI能力，实现降本增效、激活社群、深化用户关系的目标。

## 技术架构

- **架构模式**: 模块化单体 + 事件驱动连接器
- **后端**: Python + FastAPI
- **前端**: React + Vite + TypeScript
- **数据库**: PostgreSQL + ChromaDB
- **消息队列**: RabbitMQ
- **部署**: Docker + Docker Compose

## 项目结构

```
/chaiguanjia/
├── /docs/                          # 项目文档
│   └── MODULES.md                   # 模块描述文档
├── /src/                           # 源代码
│   ├── /Host/                      # 主机应用：承载所有模块的入口点
│   ├── /Modules/                   # 业务模块：所有限界上下文的根目录
│   │   ├── /ChannelManagement/     # 渠道管理模块
│   │   ├── /MessageWorkbench/      # 消息工作台模块
│   │   ├── /AIAssistant/           # AI助手模块
│   │   └── /UserManagement/        # 用户管理模块
│   └── /BuildingBlocks/            # 构建块：跨模块共享的可复用基础设施组件
│       ├── /Shared.Kernel/         # 真正通用的核心类型、接口和抽象
│       └── /Shared.Infrastructure/ # 共享的基础设施实现
├── /frontend/                      # 前端应用
├── /tests/                         # 测试
│   ├── /Architecture.Tests/        # 架构测试
│   └── /Modules.Tests/             # 模块测试
├── /scripts/                       # 脚本文件
├── /docker/                        # Docker相关文件
└── docker-compose.yml              # 开发环境编排
```

## 开发工作流

本项目严格遵循BDD驱动开发和GitHub工作流规范：

1. **分支命名**: `feature/issue<ISSUE_NUMBER>-<short-description>`
2. **提交规范**: `<type>(<scope>): <subject>`
3. **PR流程**: 必须通过CI/CD检查和代码审查

## 快速开始

### 环境要求

- Python 3.11+
- Node.js 18+
- Docker & Docker Compose
- Git

### 开发环境启动

```bash
# 克隆项目
git clone <repository-url>
cd chaiguanjia_8.1

# 启动开发环境
docker-compose up -d

# 安装后端依赖
cd src/Host
pip install -r requirements.txt

# 安装前端依赖
cd ../../frontend
npm install

# 启动开发服务器
npm run dev
```

## 贡献指南

请参考项目文档中的开发规范：
- 命名规范 (GNC-AIDD)
- 开发工作流 V1.0
- 模块化单体架构设计规范
- 高效能编码规范

## 许可证

[待定]
