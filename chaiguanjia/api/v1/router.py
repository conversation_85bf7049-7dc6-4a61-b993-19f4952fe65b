"""
API v1路由配置
"""

from fastapi import APIRouter

from . import auth, users, channels, messages, ai_assistant

# 创建v1 API路由器
api_router = APIRouter()

# 认证相关路由
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["认证"]
)

# 用户管理路由
api_router.include_router(
    users.router,
    prefix="/users",
    tags=["用户管理"]
)

# 渠道管理路由
api_router.include_router(
    channels.router,
    prefix="/channels",
    tags=["渠道管理"]
)

# 消息工作台路由
api_router.include_router(
    messages.router,
    prefix="/messages",
    tags=["消息工作台"]
)

# AI助手路由
api_router.include_router(
    ai_assistant.router,
    prefix="/ai",
    tags=["AI助手"]
)
