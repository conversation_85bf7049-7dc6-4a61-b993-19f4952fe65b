"""
用户管理API端点
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import HTTPBearer

from chaiguanjia.core.models import BaseSchema, PaginatedResponseSchema

router = APIRouter()
security = HTTPBearer()


# 请求和响应模型
class UserCreate(BaseSchema):
    """创建用户请求"""
    email: str
    username: str
    full_name: str
    password: str
    is_active: bool = True


class UserUpdate(BaseSchema):
    """更新用户请求"""
    email: Optional[str] = None
    username: Optional[str] = None
    full_name: Optional[str] = None
    is_active: Optional[bool] = None


class UserResponse(BaseSchema):
    """用户响应"""
    id: UUID
    email: str
    username: str
    full_name: str
    is_active: bool
    created_at: str
    updated_at: str


@router.get("/", response_model=PaginatedResponseSchema, summary="获取用户列表")
async def get_users(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(20, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    is_active: Optional[bool] = Query(None, description="是否激活"),
    token: str = Depends(security)
):
    """
    获取用户列表
    
    - **page**: 页码，从1开始
    - **size**: 每页数量，最大100
    - **search**: 搜索关键词，支持邮箱、用户名、全名搜索
    - **is_active**: 过滤激活状态
    """
    # TODO: 实现用户列表查询逻辑
    # 1. 验证用户权限
    # 2. 构建查询条件
    # 3. 分页查询用户
    # 4. 返回分页结果
    
    # 临时实现
    fake_users = [
        UserResponse(
            id=UUID("12345678-1234-5678-9012-123456789012"),
            email="<EMAIL>",
            username="user1",
            full_name="用户一",
            is_active=True,
            created_at="2024-01-01T00:00:00Z",
            updated_at="2024-01-01T00:00:00Z"
        ),
        UserResponse(
            id=UUID("12345678-1234-5678-9012-123456789013"),
            email="<EMAIL>",
            username="user2",
            full_name="用户二",
            is_active=True,
            created_at="2024-01-01T00:00:00Z",
            updated_at="2024-01-01T00:00:00Z"
        )
    ]
    
    return PaginatedResponseSchema(
        items=fake_users,
        pagination={
            "page": page,
            "size": size,
            "total": 2,
            "pages": 1,
            "has_next": False,
            "has_prev": False
        }
    )


@router.post("/", response_model=UserResponse, summary="创建用户")
async def create_user(
    user_data: UserCreate,
    token: str = Depends(security)
):
    """
    创建新用户
    
    - **email**: 用户邮箱，必须唯一
    - **username**: 用户名，必须唯一
    - **full_name**: 用户全名
    - **password**: 用户密码
    - **is_active**: 是否激活，默认为True
    """
    # TODO: 实现创建用户逻辑
    # 1. 验证用户权限
    # 2. 验证邮箱和用户名唯一性
    # 3. 加密密码
    # 4. 创建用户记录
    # 5. 返回用户信息
    
    # 临时实现
    return UserResponse(
        id=UUID("12345678-1234-5678-9012-123456789014"),
        email=user_data.email,
        username=user_data.username,
        full_name=user_data.full_name,
        is_active=user_data.is_active,
        created_at="2024-01-01T00:00:00Z",
        updated_at="2024-01-01T00:00:00Z"
    )


@router.get("/{user_id}", response_model=UserResponse, summary="获取用户详情")
async def get_user(
    user_id: UUID,
    token: str = Depends(security)
):
    """
    根据ID获取用户详情
    
    - **user_id**: 用户ID
    """
    # TODO: 实现获取用户详情逻辑
    # 1. 验证用户权限
    # 2. 查询用户信息
    # 3. 返回用户详情
    
    # 临时实现
    if str(user_id) == "12345678-1234-5678-9012-123456789012":
        return UserResponse(
            id=user_id,
            email="<EMAIL>",
            username="user1",
            full_name="用户一",
            is_active=True,
            created_at="2024-01-01T00:00:00Z",
            updated_at="2024-01-01T00:00:00Z"
        )
    
    raise HTTPException(
        status_code=status.HTTP_404_NOT_FOUND,
        detail="用户不存在"
    )


@router.put("/{user_id}", response_model=UserResponse, summary="更新用户")
async def update_user(
    user_id: UUID,
    user_data: UserUpdate,
    token: str = Depends(security)
):
    """
    更新用户信息
    
    - **user_id**: 用户ID
    - **email**: 用户邮箱（可选）
    - **username**: 用户名（可选）
    - **full_name**: 用户全名（可选）
    - **is_active**: 是否激活（可选）
    """
    # TODO: 实现更新用户逻辑
    # 1. 验证用户权限
    # 2. 验证用户存在
    # 3. 验证邮箱和用户名唯一性（如果更新）
    # 4. 更新用户信息
    # 5. 返回更新后的用户信息
    
    # 临时实现
    return UserResponse(
        id=user_id,
        email=user_data.email or "<EMAIL>",
        username=user_data.username or "user1",
        full_name=user_data.full_name or "用户一",
        is_active=user_data.is_active if user_data.is_active is not None else True,
        created_at="2024-01-01T00:00:00Z",
        updated_at="2024-01-01T00:00:00Z"
    )


@router.delete("/{user_id}", summary="删除用户")
async def delete_user(
    user_id: UUID,
    hard_delete: bool = Query(False, description="是否硬删除"),
    token: str = Depends(security)
):
    """
    删除用户
    
    - **user_id**: 用户ID
    - **hard_delete**: 是否硬删除，默认为软删除
    """
    # TODO: 实现删除用户逻辑
    # 1. 验证用户权限
    # 2. 验证用户存在
    # 3. 执行删除操作（软删除或硬删除）
    # 4. 返回删除结果
    
    return {"message": f"用户 {user_id} 删除成功"}


@router.post("/{user_id}/activate", summary="激活用户")
async def activate_user(
    user_id: UUID,
    token: str = Depends(security)
):
    """
    激活用户
    
    - **user_id**: 用户ID
    """
    # TODO: 实现激活用户逻辑
    return {"message": f"用户 {user_id} 激活成功"}


@router.post("/{user_id}/deactivate", summary="停用用户")
async def deactivate_user(
    user_id: UUID,
    token: str = Depends(security)
):
    """
    停用用户
    
    - **user_id**: 用户ID
    """
    # TODO: 实现停用用户逻辑
    return {"message": f"用户 {user_id} 停用成功"}
