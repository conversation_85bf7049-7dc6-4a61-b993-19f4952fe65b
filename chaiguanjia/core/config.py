"""
应用配置管理
"""
import os
from functools import lru_cache
from typing import List, Optional

from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基础配置
    app_name: str = Field(default="chaiguanjia", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    app_env: str = Field(default="development", description="应用环境")
    debug: bool = Field(default=False, description="调试模式")
    secret_key: str = Field(description="应用密钥")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8000, description="服务器端口")
    frontend_url: str = Field(default="http://localhost:3000", description="前端URL")
    allowed_hosts: List[str] = Field(default=["*"], description="允许的主机")
    
    # 数据库配置
    database_url: str = Field(description="数据库连接URL")
    db_host: str = Field(default="localhost", description="数据库主机")
    db_port: int = Field(default=5432, description="数据库端口")
    db_name: str = Field(default="chaiguanjia_db", description="数据库名称")
    db_user: str = Field(default="chaiguanjia_user", description="数据库用户")
    db_password: str = Field(description="数据库密码")
    
    # ChromaDB 配置
    chroma_db_path: str = Field(default="./chroma_db", description="ChromaDB路径")
    chroma_db_host: str = Field(default="localhost", description="ChromaDB主机")
    chroma_db_port: int = Field(default=8001, description="ChromaDB端口")
    
    # Redis 配置
    redis_url: Optional[str] = Field(default=None, description="Redis连接URL")
    redis_host: str = Field(default="localhost", description="Redis主机")
    redis_port: int = Field(default=6379, description="Redis端口")
    redis_db: int = Field(default=0, description="Redis数据库")
    
    # RabbitMQ 配置
    rabbitmq_url: str = Field(description="RabbitMQ连接URL")
    rabbitmq_host: str = Field(default="localhost", description="RabbitMQ主机")
    rabbitmq_port: int = Field(default=5672, description="RabbitMQ端口")
    rabbitmq_user: str = Field(default="guest", description="RabbitMQ用户")
    rabbitmq_password: str = Field(default="guest", description="RabbitMQ密码")
    rabbitmq_vhost: str = Field(default="/", description="RabbitMQ虚拟主机")
    
    # AI 服务配置
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API密钥")
    openai_api_base: str = Field(default="https://api.openai.com/v1", description="OpenAI API基础URL")
    openai_model: str = Field(default="gpt-3.5-turbo", description="OpenAI模型")
    
    # 第三方平台配置
    xianyu_app_id: Optional[str] = Field(default=None, description="闲鱼应用ID")
    xianyu_app_secret: Optional[str] = Field(default=None, description="闲鱼应用密钥")
    xianyu_redirect_uri: Optional[str] = Field(default=None, description="闲鱼回调URI")
    
    wechat_app_id: Optional[str] = Field(default=None, description="微信应用ID")
    wechat_app_secret: Optional[str] = Field(default=None, description="微信应用密钥")
    
    douyin_app_id: Optional[str] = Field(default=None, description="抖音应用ID")
    douyin_app_secret: Optional[str] = Field(default=None, description="抖音应用密钥")
    
    # JWT 配置
    jwt_secret_key: str = Field(description="JWT密钥")
    jwt_algorithm: str = Field(default="HS256", description="JWT算法")
    jwt_access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间(分钟)")
    jwt_refresh_token_expire_days: int = Field(default=7, description="刷新令牌过期时间(天)")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(default="json", description="日志格式")
    log_file: Optional[str] = Field(default=None, description="日志文件路径")
    
    # CORS 配置
    cors_origins: List[str] = Field(
        default=["http://localhost:3000", "http://127.0.0.1:3000"],
        description="CORS允许的源"
    )
    cors_allow_credentials: bool = Field(default=True, description="CORS允许凭证")
    cors_allow_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        description="CORS允许的方法"
    )
    cors_allow_headers: List[str] = Field(default=["*"], description="CORS允许的头部")
    
    # 文件上传配置
    upload_max_size: int = Field(default=10485760, description="上传文件最大大小(字节)")
    upload_allowed_extensions: List[str] = Field(
        default=["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx"],
        description="允许上传的文件扩展名"
    )
    upload_path: str = Field(default="uploads/", description="上传文件路径")
    
    # 监控配置
    sentry_dsn: Optional[str] = Field(default=None, description="Sentry DSN")
    prometheus_enabled: bool = Field(default=False, description="启用Prometheus")
    prometheus_port: int = Field(default=9090, description="Prometheus端口")
    
    # 开发工具配置
    enable_swagger: bool = Field(default=True, description="启用Swagger文档")
    enable_redoc: bool = Field(default=True, description="启用ReDoc文档")
    enable_debug_toolbar: bool = Field(default=False, description="启用调试工具栏")
    
    # 测试配置
    test_database_url: Optional[str] = Field(default=None, description="测试数据库URL")
    
    @validator("app_env")
    def validate_app_env(cls, v):
        """验证应用环境"""
        allowed_envs = ["development", "testing", "staging", "production"]
        if v not in allowed_envs:
            raise ValueError(f"app_env must be one of {allowed_envs}")
        return v
    
    @validator("log_level")
    def validate_log_level(cls, v):
        """验证日志级别"""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"log_level must be one of {allowed_levels}")
        return v.upper()
    
    @validator("log_format")
    def validate_log_format(cls, v):
        """验证日志格式"""
        allowed_formats = ["json", "text"]
        if v not in allowed_formats:
            raise ValueError(f"log_format must be one of {allowed_formats}")
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """获取应用配置实例（单例模式）"""
    return Settings()
