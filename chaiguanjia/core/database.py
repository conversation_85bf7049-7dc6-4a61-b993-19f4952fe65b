"""
数据库连接和配置
"""
from typing import AsyncGenerator, Optional

import structlog
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from chaiguanjia.core.config import get_settings

logger = structlog.get_logger(__name__)

# 数据库引擎
engine: Optional[create_async_engine] = None
async_session_maker: Optional[async_sessionmaker] = None

# 基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()


async def init_database() -> None:
    """初始化数据库连接"""
    global engine, async_session_maker
    
    settings = get_settings()
    
    logger.info("初始化数据库连接", database_url=settings.database_url)
    
    # 创建异步引擎
    engine = create_async_engine(
        settings.database_url,
        echo=settings.debug,
        pool_pre_ping=True,
        pool_recycle=300,
        pool_size=20,
        max_overflow=0,
    )
    
    # 创建会话工厂
    async_session_maker = async_sessionmaker(
        engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )
    
    logger.info("数据库连接初始化完成")


async def close_database() -> None:
    """关闭数据库连接"""
    global engine
    
    if engine:
        logger.info("关闭数据库连接")
        await engine.dispose()
        logger.info("数据库连接已关闭")


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话"""
    if not async_session_maker:
        raise RuntimeError("数据库未初始化")
    
    async with async_session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self.engine = None
        self.session_maker = None
    
    async def connect(self, database_url: str) -> None:
        """连接数据库"""
        self.engine = create_async_engine(
            database_url,
            echo=False,
            pool_pre_ping=True,
        )
        
        self.session_maker = async_sessionmaker(
            self.engine,
            class_=AsyncSession,
            expire_on_commit=False,
        )
        
        logger.info("数据库管理器连接成功")
    
    async def disconnect(self) -> None:
        """断开数据库连接"""
        if self.engine:
            await self.engine.dispose()
            logger.info("数据库管理器断开连接")
    
    async def get_session(self) -> AsyncGenerator[AsyncSession, None]:
        """获取数据库会话"""
        if not self.session_maker:
            raise RuntimeError("数据库管理器未连接")
        
        async with self.session_maker() as session:
            try:
                yield session
            except Exception:
                await session.rollback()
                raise
            finally:
                await session.close()


# 全局数据库管理器实例
db_manager = DatabaseManager()


async def create_tables() -> None:
    """创建数据库表"""
    if not engine:
        raise RuntimeError("数据库引擎未初始化")
    
    logger.info("创建数据库表")
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    logger.info("数据库表创建完成")


async def drop_tables() -> None:
    """删除数据库表"""
    if not engine:
        raise RuntimeError("数据库引擎未初始化")
    
    logger.info("删除数据库表")
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    logger.info("数据库表删除完成")
