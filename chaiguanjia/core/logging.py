"""
日志配置模块
"""
import logging
import sys
from typing import Any, Dict

import structlog
from rich.console import Console
from rich.logging import <PERSON>Hand<PERSON>


def configure_logging(log_level: str = "INFO", log_format: str = "json") -> None:
    """配置应用日志"""
    
    # 配置标准库日志
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level.upper()),
    )
    
    # 配置 structlog
    if log_format == "json":
        configure_json_logging(log_level)
    else:
        configure_text_logging(log_level)


def configure_json_logging(log_level: str) -> None:
    """配置JSON格式日志"""
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def configure_text_logging(log_level: str) -> None:
    """配置文本格式日志（开发环境友好）"""
    console = Console()
    
    # 配置 Rich 处理器
    rich_handler = RichHandler(
        console=console,
        show_time=True,
        show_path=True,
        markup=True,
        rich_tracebacks=True,
    )
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.handlers = [rich_handler]
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # 配置 structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.dev.ConsoleRenderer(colors=True)
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


def get_logger(name: str = None) -> structlog.BoundLogger:
    """获取结构化日志器"""
    return structlog.get_logger(name)


class LoggingMixin:
    """日志混入类"""
    
    @property
    def logger(self) -> structlog.BoundLogger:
        """获取当前类的日志器"""
        return get_logger(self.__class__.__name__)


def log_function_call(func_name: str, **kwargs: Any) -> None:
    """记录函数调用日志"""
    logger = get_logger()
    logger.info(
        "函数调用",
        function=func_name,
        **kwargs
    )


def log_error(error: Exception, context: Dict[str, Any] = None) -> None:
    """记录错误日志"""
    logger = get_logger()
    logger.error(
        "发生错误",
        error_type=type(error).__name__,
        error_message=str(error),
        context=context or {},
        exc_info=True
    )
