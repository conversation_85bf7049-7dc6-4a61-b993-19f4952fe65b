"""
消息队列核心模块
基于RabbitMQ的异步消息处理框架
"""

import asyncio
import json
import logging
from typing import Any, Callable, Dict, Optional, Union
from datetime import datetime
import aio_pika
from aio_pika import Message, DeliveryMode, ExchangeType
from aio_pika.abc import AbstractConnection, AbstractChannel, AbstractExchange, AbstractQueue
from pydantic import BaseModel

from chaiguanjia.core.config import get_settings

logger = logging.getLogger(__name__)


class MessageEvent(BaseModel):
    """消息事件基类"""
    event_type: str
    event_id: str
    timestamp: datetime
    source: str
    data: Dict[str, Any]
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None


class MessageCommand(BaseModel):
    """消息命令基类"""
    command_type: str
    command_id: str
    timestamp: datetime
    source: str
    data: Dict[str, Any]
    correlation_id: Optional[str] = None
    reply_to: Optional[str] = None


class MessageQueueManager:
    """消息队列管理器"""
    
    def __init__(self):
        self.connection: Optional[AbstractConnection] = None
        self.channel: Optional[AbstractChannel] = None
        self.exchanges: Dict[str, AbstractExchange] = {}
        self.queues: Dict[str, AbstractQueue] = {}
        self.consumers: Dict[str, Callable] = {}
        self._is_connected = False
    
    async def connect(self) -> None:
        """连接到RabbitMQ"""
        try:
            settings = get_settings()
            
            # 构建连接URL
            connection_url = settings.rabbitmq_url
            
            # 建立连接
            self.connection = await aio_pika.connect_robust(
                connection_url,
                heartbeat=60,
                blocked_connection_timeout=300,
            )
            
            # 创建通道
            self.channel = await self.connection.channel()
            await self.channel.set_qos(prefetch_count=10)
            
            # 声明交换机
            await self._declare_exchanges()
            
            # 声明队列
            await self._declare_queues()
            
            self._is_connected = True
            logger.info("✓ RabbitMQ 连接成功")
            
        except Exception as e:
            logger.error(f"✗ RabbitMQ 连接失败: {e}")
            raise
    
    async def disconnect(self) -> None:
        """断开连接"""
        try:
            if self.connection and not self.connection.is_closed:
                await self.connection.close()
            self._is_connected = False
            logger.info("RabbitMQ 连接已断开")
        except Exception as e:
            logger.error(f"断开RabbitMQ连接时出错: {e}")
    
    async def _declare_exchanges(self) -> None:
        """声明交换机"""
        if not self.channel:
            raise RuntimeError("Channel not initialized")
        
        # 事件交换机 (Topic)
        self.exchanges['events'] = await self.channel.declare_exchange(
            'chaiguanjia.events',
            ExchangeType.TOPIC,
            durable=True
        )
        
        # 命令交换机 (Direct)
        self.exchanges['commands'] = await self.channel.declare_exchange(
            'chaiguanjia.commands',
            ExchangeType.DIRECT,
            durable=True
        )
        
        # 死信交换机
        self.exchanges['dlx'] = await self.channel.declare_exchange(
            'chaiguanjia.dlx',
            ExchangeType.DIRECT,
            durable=True
        )
    
    async def _declare_queues(self) -> None:
        """声明队列"""
        if not self.channel:
            raise RuntimeError("Channel not initialized")
        
        # 事件队列
        event_queues = [
            'channel.events',
            'message.events', 
            'ai.events',
            'user.events'
        ]
        
        for queue_name in event_queues:
            self.queues[queue_name] = await self.channel.declare_queue(
                queue_name,
                durable=True,
                arguments={
                    'x-message-ttl': 3600000,  # 1小时TTL
                    'x-dead-letter-exchange': 'chaiguanjia.dlx',
                    'x-dead-letter-routing-key': f'{queue_name}.dlq'
                }
            )
        
        # 命令队列
        command_queues = [
            'channel.commands',
            'message.commands',
            'ai.commands'
        ]
        
        for queue_name in command_queues:
            self.queues[queue_name] = await self.channel.declare_queue(
                queue_name,
                durable=True,
                arguments={
                    'x-message-ttl': 300000,  # 5分钟TTL
                    'x-dead-letter-exchange': 'chaiguanjia.dlx',
                    'x-dead-letter-routing-key': f'{queue_name}.dlq'
                }
            )
        
        # 死信队列
        self.queues['dead_letter_queue'] = await self.channel.declare_queue(
            'dead_letter_queue',
            durable=True
        )
    
    async def publish_event(
        self,
        event: MessageEvent,
        routing_key: str
    ) -> None:
        """发布事件"""
        if not self._is_connected:
            raise RuntimeError("Not connected to RabbitMQ")
        
        try:
            message_body = event.model_dump_json().encode()
            message = Message(
                message_body,
                delivery_mode=DeliveryMode.PERSISTENT,
                timestamp=datetime.now(),
                message_id=event.event_id,
                correlation_id=event.correlation_id,
                reply_to=event.reply_to
            )
            
            await self.exchanges['events'].publish(
                message,
                routing_key=routing_key
            )
            
            logger.debug(f"事件已发布: {event.event_type} -> {routing_key}")
            
        except Exception as e:
            logger.error(f"发布事件失败: {e}")
            raise
    
    async def publish_command(
        self,
        command: MessageCommand,
        routing_key: str
    ) -> None:
        """发布命令"""
        if not self._is_connected:
            raise RuntimeError("Not connected to RabbitMQ")
        
        try:
            message_body = command.model_dump_json().encode()
            message = Message(
                message_body,
                delivery_mode=DeliveryMode.PERSISTENT,
                timestamp=datetime.now(),
                message_id=command.command_id,
                correlation_id=command.correlation_id,
                reply_to=command.reply_to
            )
            
            await self.exchanges['commands'].publish(
                message,
                routing_key=routing_key
            )
            
            logger.debug(f"命令已发布: {command.command_type} -> {routing_key}")
            
        except Exception as e:
            logger.error(f"发布命令失败: {e}")
            raise
    
    async def subscribe_to_queue(
        self,
        queue_name: str,
        callback: Callable[[Dict[str, Any]], None]
    ) -> None:
        """订阅队列"""
        if not self._is_connected:
            raise RuntimeError("Not connected to RabbitMQ")
        
        if queue_name not in self.queues:
            raise ValueError(f"Queue {queue_name} not found")
        
        queue = self.queues[queue_name]
        
        async def message_handler(message: aio_pika.IncomingMessage):
            async with message.process():
                try:
                    # 解析消息
                    message_data = json.loads(message.body.decode())
                    
                    # 调用回调函数
                    await callback(message_data)
                    
                    logger.debug(f"消息处理成功: {queue_name}")
                    
                except Exception as e:
                    logger.error(f"消息处理失败 {queue_name}: {e}")
                    raise
        
        # 开始消费
        await queue.consume(message_handler)
        self.consumers[queue_name] = callback
        
        logger.info(f"开始监听队列: {queue_name}")
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            if not self._is_connected or not self.connection or self.connection.is_closed:
                return {
                    'status': 'unhealthy',
                    'message': 'Not connected to RabbitMQ'
                }
            
            # 检查通道状态
            if not self.channel or self.channel.is_closed:
                return {
                    'status': 'unhealthy',
                    'message': 'Channel is closed'
                }
            
            return {
                'status': 'healthy',
                'exchanges': len(self.exchanges),
                'queues': len(self.queues),
                'consumers': len(self.consumers)
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'message': str(e)
            }


# 全局消息队列管理器实例
message_queue_manager = MessageQueueManager()


async def get_message_queue() -> MessageQueueManager:
    """获取消息队列管理器实例"""
    if not message_queue_manager._is_connected:
        await message_queue_manager.connect()
    return message_queue_manager
