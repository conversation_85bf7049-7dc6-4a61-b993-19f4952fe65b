"""
数据库模型基类
提供通用的模型基础功能和字段
"""

from datetime import datetime
from typing import Any, Dict, Optional
from uuid import UUID, uuid4

from sqlalchemy import Column, DateTime, String, Boolean, Text, func
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from sqlalchemy.ext.declarative import declarative_base, declared_attr
from sqlalchemy.orm import Session
from pydantic import BaseModel, ConfigDict


# SQLAlchemy基类
Base = declarative_base()


class TimestampMixin:
    """时间戳混入类"""
    
    created_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    
    updated_at = Column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )


class UUIDMixin:
    """UUID主键混入类"""
    
    id = Column(
        PostgresUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        nullable=False,
        comment="主键ID"
    )


class SoftDeleteMixin:
    """软删除混入类"""
    
    is_deleted = Column(
        Boolean,
        default=False,
        nullable=False,
        comment="是否已删除"
    )
    
    deleted_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="删除时间"
    )


class BaseModel(Base, UUIDMixin, TimestampMixin, SoftDeleteMixin):
    """基础模型类"""
    
    __abstract__ = True
    
    @declared_attr
    def __tablename__(cls) -> str:
        """自动生成表名"""
        return cls.__name__.lower()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def update(self, **kwargs) -> None:
        """更新模型属性"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def soft_delete(self) -> None:
        """软删除"""
        self.is_deleted = True
        self.deleted_at = datetime.utcnow()
    
    def restore(self) -> None:
        """恢复软删除"""
        self.is_deleted = False
        self.deleted_at = None
    
    @classmethod
    def create(cls, db: Session, **kwargs) -> "BaseModel":
        """创建实例"""
        instance = cls(**kwargs)
        db.add(instance)
        db.commit()
        db.refresh(instance)
        return instance
    
    @classmethod
    def get_by_id(cls, db: Session, id: UUID) -> Optional["BaseModel"]:
        """根据ID获取实例"""
        return db.query(cls).filter(
            cls.id == id,
            cls.is_deleted == False
        ).first()
    
    @classmethod
    def get_all(cls, db: Session, skip: int = 0, limit: int = 100) -> list["BaseModel"]:
        """获取所有实例"""
        return db.query(cls).filter(
            cls.is_deleted == False
        ).offset(skip).limit(limit).all()
    
    def save(self, db: Session) -> "BaseModel":
        """保存实例"""
        db.add(self)
        db.commit()
        db.refresh(self)
        return self
    
    def delete(self, db: Session, hard: bool = False) -> None:
        """删除实例"""
        if hard:
            db.delete(self)
        else:
            self.soft_delete()
        db.commit()


# Pydantic基础模型
class BaseSchema(BaseModel):
    """基础Pydantic模型"""
    
    model_config = ConfigDict(
        from_attributes=True,
        validate_assignment=True,
        arbitrary_types_allowed=True,
        str_strip_whitespace=True
    )


class TimestampSchema(BaseSchema):
    """时间戳Schema"""

    __allow_unmapped__ = True

    created_at: datetime
    updated_at: datetime


class UUIDSchema(BaseSchema):
    """UUID Schema"""
    
    id: UUID


class BaseResponseSchema(UUIDSchema, TimestampSchema):
    """基础响应Schema"""
    
    pass


class PaginationSchema(BaseSchema):
    """分页Schema"""
    
    page: int = 1
    size: int = 20
    total: int = 0
    pages: int = 0
    has_next: bool = False
    has_prev: bool = False


class PaginatedResponseSchema(BaseSchema):
    """分页响应Schema"""
    
    items: list[Any]
    pagination: PaginationSchema


# 数据库操作基类
class BaseRepository:
    """基础仓储类"""
    
    def __init__(self, db: Session, model_class: type[BaseModel]):
        self.db = db
        self.model_class = model_class
    
    def create(self, **kwargs) -> BaseModel:
        """创建"""
        return self.model_class.create(self.db, **kwargs)
    
    def get_by_id(self, id: UUID) -> Optional[BaseModel]:
        """根据ID获取"""
        return self.model_class.get_by_id(self.db, id)
    
    def get_all(self, skip: int = 0, limit: int = 100) -> list[BaseModel]:
        """获取所有"""
        return self.model_class.get_all(self.db, skip, limit)
    
    def update(self, id: UUID, **kwargs) -> Optional[BaseModel]:
        """更新"""
        instance = self.get_by_id(id)
        if instance:
            instance.update(**kwargs)
            return instance.save(self.db)
        return None
    
    def delete(self, id: UUID, hard: bool = False) -> bool:
        """删除"""
        instance = self.get_by_id(id)
        if instance:
            instance.delete(self.db, hard)
            return True
        return False
    
    def count(self) -> int:
        """计数"""
        return self.db.query(self.model_class).filter(
            self.model_class.is_deleted == False
        ).count()
    
    def paginate(self, page: int = 1, size: int = 20) -> PaginatedResponseSchema:
        """分页查询"""
        total = self.count()
        pages = (total + size - 1) // size
        skip = (page - 1) * size
        
        items = self.get_all(skip=skip, limit=size)
        
        pagination = PaginationSchema(
            page=page,
            size=size,
            total=total,
            pages=pages,
            has_next=page < pages,
            has_prev=page > 1
        )
        
        return PaginatedResponseSchema(
            items=items,
            pagination=pagination
        )


# 服务基类
class BaseService:
    """基础服务类"""
    
    def __init__(self, repository: BaseRepository):
        self.repository = repository
    
    async def create(self, **kwargs) -> BaseModel:
        """创建"""
        return self.repository.create(**kwargs)
    
    async def get_by_id(self, id: UUID) -> Optional[BaseModel]:
        """根据ID获取"""
        return self.repository.get_by_id(id)
    
    async def get_all(self, skip: int = 0, limit: int = 100) -> list[BaseModel]:
        """获取所有"""
        return self.repository.get_all(skip, limit)
    
    async def update(self, id: UUID, **kwargs) -> Optional[BaseModel]:
        """更新"""
        return self.repository.update(id, **kwargs)
    
    async def delete(self, id: UUID, hard: bool = False) -> bool:
        """删除"""
        return self.repository.delete(id, hard)
    
    async def paginate(self, page: int = 1, size: int = 20) -> PaginatedResponseSchema:
        """分页查询"""
        return self.repository.paginate(page, size)
