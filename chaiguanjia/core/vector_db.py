"""
向量数据库管理 (ChromaDB)
"""
from typing import Dict, List, Optional

import chromadb
import structlog
from chromadb.config import Settings

from chaiguanjia.core.config import get_settings

logger = structlog.get_logger(__name__)

# 全局客户端
client: Optional[chromadb.Client] = None


async def init_vector_db() -> None:
    """初始化向量数据库"""
    global client
    
    settings = get_settings()
    
    logger.info("初始化向量数据库", chroma_db_path=settings.chroma_db_path)
    
    try:
        # 创建 ChromaDB 客户端
        client = chromadb.PersistentClient(
            path=settings.chroma_db_path,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # 创建默认集合
        await create_default_collections()
        
        logger.info("向量数据库初始化完成")
        
    except Exception as e:
        logger.error("向量数据库初始化失败", error=str(e))
        raise


async def create_default_collections() -> None:
    """创建默认集合"""
    if not client:
        raise RuntimeError("向量数据库客户端未初始化")
    
    collections = [
        {
            "name": "knowledge_base",
            "metadata": {"description": "知识库问答对"}
        },
        {
            "name": "conversation_history",
            "metadata": {"description": "会话历史记录"}
        },
        {
            "name": "user_intents",
            "metadata": {"description": "用户意图分析"}
        },
        {
            "name": "ai_responses",
            "metadata": {"description": "AI回复模板"}
        }
    ]
    
    for collection_info in collections:
        try:
            collection = client.get_or_create_collection(
                name=collection_info["name"],
                metadata=collection_info["metadata"]
            )
            logger.info("创建集合", collection=collection_info["name"])
        except Exception as e:
            logger.error(
                "创建集合失败",
                collection=collection_info["name"],
                error=str(e)
            )


class VectorDBManager:
    """向量数据库管理器"""
    
    def __init__(self):
        self.client = client
    
    async def add_documents(
        self,
        collection_name: str,
        documents: List[str],
        metadatas: Optional[List[Dict]] = None,
        ids: Optional[List[str]] = None
    ) -> None:
        """添加文档到集合"""
        if not self.client:
            raise RuntimeError("向量数据库客户端未初始化")
        
        try:
            collection = self.client.get_collection(collection_name)
            
            collection.add(
                documents=documents,
                metadatas=metadatas,
                ids=ids
            )
            
            logger.info(
                "添加文档到集合",
                collection=collection_name,
                count=len(documents)
            )
            
        except Exception as e:
            logger.error(
                "添加文档失败",
                collection=collection_name,
                error=str(e)
            )
            raise
    
    async def query_documents(
        self,
        collection_name: str,
        query_texts: List[str],
        n_results: int = 10,
        where: Optional[Dict] = None
    ) -> Dict:
        """查询文档"""
        if not self.client:
            raise RuntimeError("向量数据库客户端未初始化")
        
        try:
            collection = self.client.get_collection(collection_name)
            
            results = collection.query(
                query_texts=query_texts,
                n_results=n_results,
                where=where
            )
            
            logger.info(
                "查询文档",
                collection=collection_name,
                query_count=len(query_texts),
                result_count=len(results.get("documents", []))
            )
            
            return results
            
        except Exception as e:
            logger.error(
                "查询文档失败",
                collection=collection_name,
                error=str(e)
            )
            raise
    
    async def update_documents(
        self,
        collection_name: str,
        ids: List[str],
        documents: Optional[List[str]] = None,
        metadatas: Optional[List[Dict]] = None
    ) -> None:
        """更新文档"""
        if not self.client:
            raise RuntimeError("向量数据库客户端未初始化")
        
        try:
            collection = self.client.get_collection(collection_name)
            
            collection.update(
                ids=ids,
                documents=documents,
                metadatas=metadatas
            )
            
            logger.info(
                "更新文档",
                collection=collection_name,
                count=len(ids)
            )
            
        except Exception as e:
            logger.error(
                "更新文档失败",
                collection=collection_name,
                error=str(e)
            )
            raise
    
    async def delete_documents(
        self,
        collection_name: str,
        ids: List[str]
    ) -> None:
        """删除文档"""
        if not self.client:
            raise RuntimeError("向量数据库客户端未初始化")
        
        try:
            collection = self.client.get_collection(collection_name)
            
            collection.delete(ids=ids)
            
            logger.info(
                "删除文档",
                collection=collection_name,
                count=len(ids)
            )
            
        except Exception as e:
            logger.error(
                "删除文档失败",
                collection=collection_name,
                error=str(e)
            )
            raise
    
    async def get_collection_info(self, collection_name: str) -> Dict:
        """获取集合信息"""
        if not self.client:
            raise RuntimeError("向量数据库客户端未初始化")
        
        try:
            collection = self.client.get_collection(collection_name)
            
            count = collection.count()
            
            return {
                "name": collection_name,
                "count": count,
                "metadata": collection.metadata
            }
            
        except Exception as e:
            logger.error(
                "获取集合信息失败",
                collection=collection_name,
                error=str(e)
            )
            raise


# 全局向量数据库管理器实例
vector_db_manager = VectorDBManager()
