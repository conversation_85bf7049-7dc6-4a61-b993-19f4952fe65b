"""
渠道管理模块

负责管理各种平台渠道的连接、配置和状态监控
"""

from .models import Channel, ChannelType, ChannelStatus
from .schemas import (
    ChannelCreateRequest,
    ChannelUpdateRequest,
    ChannelResponse,
    ChannelListResponse,
    ChannelStatusResponse
)
from .service import ChannelService
from .repository import ChannelRepository

__all__ = [
    "Channel",
    "ChannelType",
    "ChannelStatus",
    "ChannelCreateRequest",
    "ChannelUpdateRequest",
    "ChannelResponse",
    "ChannelListResponse",
    "ChannelStatusResponse",
    "ChannelService",
    "ChannelRepository"
]
