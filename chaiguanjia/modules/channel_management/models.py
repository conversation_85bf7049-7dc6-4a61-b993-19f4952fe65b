"""
渠道管理数据模型
"""

import enum
from datetime import datetime
from typing import Optional, Dict, Any

from sqlalchemy import Column, String, Enum, JSON, Text, Boolean, DateTime
from sqlalchemy.orm import relationship

from chaiguanjia.core.models import BaseModel


class ChannelType(str, enum.Enum):
    """渠道类型枚举"""
    XIANYU = "xianyu"  # 闲鱼
    WECHAT = "wechat"  # 微信
    DOUYIN = "douyin"  # 抖音
    XIAOHONGSHU = "xiaohongshu"  # 小红书


class ChannelStatus(str, enum.Enum):
    """渠道状态枚举"""
    DISCONNECTED = "disconnected"  # 未连接
    CONNECTING = "connecting"      # 连接中
    CONNECTED = "connected"        # 已连接
    ERROR = "error"               # 连接错误
    DISABLED = "disabled"         # 已禁用


class Channel(BaseModel):
    """渠道模型"""
    
    __tablename__ = "channels"
    
    # 基本信息
    name = Column(
        String(100),
        nullable=False,
        comment="渠道名称/别名"
    )
    
    type = Column(
        Enum(ChannelType),
        nullable=False,
        comment="渠道类型"
    )
    
    platform_account_id = Column(
        String(200),
        nullable=False,
        comment="平台账号ID"
    )
    
    platform_account_name = Column(
        String(200),
        nullable=True,
        comment="平台账号名称"
    )
    
    # 状态信息
    status = Column(
        Enum(ChannelStatus),
        default=ChannelStatus.DISCONNECTED,
        nullable=False,
        comment="连接状态"
    )
    
    last_sync_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="最后同步时间"
    )
    
    last_error = Column(
        Text,
        nullable=True,
        comment="最后错误信息"
    )
    
    # 配置信息
    config = Column(
        JSON,
        nullable=True,
        comment="渠道配置信息"
    )
    
    credentials = Column(
        JSON,
        nullable=True,
        comment="认证凭据(加密存储)"
    )
    
    # 统计信息
    message_count = Column(
        "message_count",
        nullable=False,
        default=0,
        comment="消息总数"
    )
    
    is_enabled = Column(
        Boolean,
        default=True,
        nullable=False,
        comment="是否启用"
    )
    
    def __repr__(self) -> str:
        return f"<Channel(id={self.id}, name={self.name}, type={self.type}, status={self.status})>"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典，排除敏感信息"""
        data = super().to_dict()
        # 移除敏感的认证信息
        if 'credentials' in data:
            data['credentials'] = "***" if data['credentials'] else None
        return data
    
    def update_status(self, status: ChannelStatus, error: Optional[str] = None) -> None:
        """更新渠道状态"""
        self.status = status
        if error:
            self.last_error = error
        elif status == ChannelStatus.CONNECTED:
            self.last_error = None
            self.last_sync_at = datetime.utcnow()
    
    def increment_message_count(self) -> None:
        """增加消息计数"""
        self.message_count += 1
    
    def is_active(self) -> bool:
        """检查渠道是否活跃"""
        return (
            self.is_enabled and 
            not self.is_deleted and 
            self.status == ChannelStatus.CONNECTED
        )
    
    def get_display_name(self) -> str:
        """获取显示名称"""
        if self.name:
            return self.name
        return f"{self.type.value}_{self.platform_account_name or self.platform_account_id}"
