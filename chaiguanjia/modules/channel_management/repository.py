"""
渠道管理仓储层
"""

from typing import List, Optional
from uuid import UUID

from sqlalchemy.orm import Session
from sqlalchemy import and_

from chaiguanjia.core.models import BaseRepository
from .models import Channel, ChannelType, ChannelStatus


class ChannelRepository(BaseRepository):
    """渠道仓储"""
    
    def __init__(self, db: Session):
        super().__init__(db, Channel)
    
    def get_by_platform_account(
        self, 
        type: ChannelType, 
        platform_account_id: str
    ) -> Optional[Channel]:
        """根据平台类型和账号ID获取渠道"""
        return self.db.query(Channel).filter(
            and_(
                Channel.type == type,
                Channel.platform_account_id == platform_account_id,
                Channel.is_deleted == False
            )
        ).first()
    
    def get_by_type(self, type: ChannelType) -> List[Channel]:
        """根据渠道类型获取渠道列表"""
        return self.db.query(Channel).filter(
            and_(
                Channel.type == type,
                Channel.is_deleted == False
            )
        ).all()
    
    def get_active_channels(self) -> List[Channel]:
        """获取所有活跃的渠道"""
        return self.db.query(Channel).filter(
            and_(
                Channel.is_enabled == True,
                Channel.status == ChannelStatus.CONNECTED,
                Channel.is_deleted == False
            )
        ).all()
    
    def get_by_status(self, status: ChannelStatus) -> List[Channel]:
        """根据状态获取渠道列表"""
        return self.db.query(Channel).filter(
            and_(
                Channel.status == status,
                Channel.is_deleted == False
            )
        ).all()
    
    def update_status(
        self, 
        channel_id: UUID, 
        status: ChannelStatus, 
        error: Optional[str] = None
    ) -> Optional[Channel]:
        """更新渠道状态"""
        channel = self.get_by_id(channel_id)
        if channel:
            channel.update_status(status, error)
            return channel.save(self.db)
        return None
    
    def increment_message_count(self, channel_id: UUID) -> Optional[Channel]:
        """增加消息计数"""
        channel = self.get_by_id(channel_id)
        if channel:
            channel.increment_message_count()
            return channel.save(self.db)
        return None
    
    def search_channels(
        self, 
        name: Optional[str] = None,
        type: Optional[ChannelType] = None,
        status: Optional[ChannelStatus] = None,
        is_enabled: Optional[bool] = None
    ) -> List[Channel]:
        """搜索渠道"""
        query = self.db.query(Channel).filter(Channel.is_deleted == False)
        
        if name:
            query = query.filter(Channel.name.ilike(f"%{name}%"))
        
        if type:
            query = query.filter(Channel.type == type)
        
        if status:
            query = query.filter(Channel.status == status)
        
        if is_enabled is not None:
            query = query.filter(Channel.is_enabled == is_enabled)
        
        return query.all()
    
    def get_channels_by_user(self, user_id: UUID) -> List[Channel]:
        """获取用户的渠道列表（暂时返回所有，后续可扩展用户权限）"""
        return self.get_all()
    
    def check_name_exists(self, name: str, exclude_id: Optional[UUID] = None) -> bool:
        """检查渠道名称是否已存在"""
        query = self.db.query(Channel).filter(
            and_(
                Channel.name == name,
                Channel.is_deleted == False
            )
        )
        
        if exclude_id:
            query = query.filter(Channel.id != exclude_id)
        
        return query.first() is not None
