"""
渠道管理Pydantic模式
"""

from datetime import datetime
from typing import Optional, Dict, Any, List
from uuid import UUID

from pydantic import BaseModel, Field, validator

from .models import ChannelType, ChannelStatus


class ChannelCreateRequest(BaseModel):
    """创建渠道请求"""
    
    name: Optional[str] = Field(
        None,
        max_length=100,
        description="渠道别名"
    )
    
    type: ChannelType = Field(
        ...,
        description="渠道类型"
    )
    
    platform_account_id: str = Field(
        ...,
        max_length=200,
        description="平台账号ID"
    )
    
    platform_account_name: Optional[str] = Field(
        None,
        max_length=200,
        description="平台账号名称"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        None,
        description="渠道配置"
    )
    
    credentials: Optional[Dict[str, Any]] = Field(
        None,
        description="认证凭据"
    )
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and len(v.strip()) == 0:
            raise ValueError('渠道名称不能为空')
        return v.strip() if v else None


class ChannelUpdateRequest(BaseModel):
    """更新渠道请求"""
    
    name: Optional[str] = Field(
        None,
        max_length=100,
        description="渠道别名"
    )
    
    config: Optional[Dict[str, Any]] = Field(
        None,
        description="渠道配置"
    )
    
    credentials: Optional[Dict[str, Any]] = Field(
        None,
        description="认证凭据"
    )
    
    is_enabled: Optional[bool] = Field(
        None,
        description="是否启用"
    )
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and len(v.strip()) == 0:
            raise ValueError('渠道名称不能为空')
        return v.strip() if v else None


class ChannelResponse(BaseModel):
    """渠道响应"""
    
    id: UUID
    name: Optional[str]
    type: ChannelType
    platform_account_id: str
    platform_account_name: Optional[str]
    status: ChannelStatus
    last_sync_at: Optional[datetime]
    last_error: Optional[str]
    config: Optional[Dict[str, Any]]
    message_count: int
    is_enabled: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ChannelListResponse(BaseModel):
    """渠道列表响应"""
    
    channels: List[ChannelResponse]
    total: int


class ChannelStatusResponse(BaseModel):
    """渠道状态响应"""
    
    channel_id: UUID
    status: ChannelStatus
    last_sync_at: Optional[datetime]
    last_error: Optional[str]
    message_count: int
    is_enabled: bool


class XianyuAuthRequest(BaseModel):
    """闲鱼授权请求"""
    
    cookies: str = Field(
        ...,
        description="闲鱼登录cookies"
    )
    
    user_agent: Optional[str] = Field(
        None,
        description="用户代理字符串"
    )
    
    device_id: Optional[str] = Field(
        None,
        description="设备ID"
    )
    
    @validator('cookies')
    def validate_cookies(cls, v):
        if not v or len(v.strip()) == 0:
            raise ValueError('cookies不能为空')
        return v.strip()


class XianyuAuthResponse(BaseModel):
    """闲鱼授权响应"""
    
    success: bool
    channel_id: Optional[UUID] = None
    user_info: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class ChannelConnectRequest(BaseModel):
    """渠道连接请求"""
    
    force: bool = Field(
        False,
        description="是否强制重连"
    )


class ChannelConnectResponse(BaseModel):
    """渠道连接响应"""
    
    success: bool
    status: ChannelStatus
    message: str
    error: Optional[str] = None
