"""
渠道管理服务层
"""

import json
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import HTTPException, status
import structlog

from chaiguanjia.core.models import BaseService
from .models import Channel, ChannelType, ChannelStatus
from .repository import ChannelRepository
from .schemas import (
    ChannelCreateRequest,
    ChannelUpdateRequest,
    ChannelResponse,
    ChannelListResponse,
    ChannelStatusResponse,
    XianyuAuthRequest,
    XianyuAuthResponse
)

logger = structlog.get_logger(__name__)


class ChannelService(BaseService):
    """渠道管理服务"""
    
    def __init__(self, repository: ChannelRepository):
        super().__init__(repository)
        self.repository = repository
    
    async def create_channel(self, request: ChannelCreateRequest) -> ChannelResponse:
        """创建渠道"""
        logger.info("创建渠道", type=request.type, platform_account_id=request.platform_account_id)
        
        # 检查是否已存在相同的平台账号
        existing = self.repository.get_by_platform_account(
            request.type, 
            request.platform_account_id
        )
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"平台账号 {request.platform_account_id} 已存在"
            )
        
        # 检查渠道名称是否重复
        if request.name and self.repository.check_name_exists(request.name):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"渠道名称 {request.name} 已存在"
            )
        
        # 创建渠道
        channel_data = request.dict(exclude_none=True)
        channel = self.repository.create(**channel_data)
        
        logger.info("渠道创建成功", channel_id=channel.id)
        return ChannelResponse.from_orm(channel)
    
    async def get_channels(self) -> ChannelListResponse:
        """获取渠道列表"""
        logger.info("获取渠道列表")
        
        channels = self.repository.get_all()
        channel_responses = [ChannelResponse.from_orm(channel) for channel in channels]
        
        return ChannelListResponse(
            channels=channel_responses,
            total=len(channel_responses)
        )
    
    async def get_channel(self, channel_id: UUID) -> ChannelResponse:
        """获取渠道详情"""
        logger.info("获取渠道详情", channel_id=channel_id)
        
        channel = self.repository.get_by_id(channel_id)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="渠道不存在"
            )
        
        return ChannelResponse.from_orm(channel)
    
    async def update_channel(
        self, 
        channel_id: UUID, 
        request: ChannelUpdateRequest
    ) -> ChannelResponse:
        """更新渠道"""
        logger.info("更新渠道", channel_id=channel_id)
        
        channel = self.repository.get_by_id(channel_id)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="渠道不存在"
            )
        
        # 检查渠道名称是否重复
        if request.name and self.repository.check_name_exists(request.name, channel_id):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"渠道名称 {request.name} 已存在"
            )
        
        # 更新渠道
        update_data = request.dict(exclude_none=True)
        updated_channel = self.repository.update(channel_id, **update_data)
        
        logger.info("渠道更新成功", channel_id=channel_id)
        return ChannelResponse.from_orm(updated_channel)
    
    async def delete_channel(self, channel_id: UUID) -> bool:
        """删除渠道"""
        logger.info("删除渠道", channel_id=channel_id)
        
        channel = self.repository.get_by_id(channel_id)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="渠道不存在"
            )
        
        # 软删除渠道
        success = self.repository.delete(channel_id, hard=False)
        
        if success:
            logger.info("渠道删除成功", channel_id=channel_id)
        
        return success
    
    async def get_channel_status(self, channel_id: UUID) -> ChannelStatusResponse:
        """获取渠道状态"""
        logger.info("获取渠道状态", channel_id=channel_id)
        
        channel = self.repository.get_by_id(channel_id)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="渠道不存在"
            )
        
        return ChannelStatusResponse(
            channel_id=channel.id,
            status=channel.status,
            last_sync_at=channel.last_sync_at,
            last_error=channel.last_error,
            message_count=channel.message_count,
            is_enabled=channel.is_enabled
        )
    
    async def connect_channel(self, channel_id: UUID, force: bool = False) -> Dict[str, Any]:
        """连接渠道"""
        logger.info("连接渠道", channel_id=channel_id, force=force)
        
        channel = self.repository.get_by_id(channel_id)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="渠道不存在"
            )
        
        if not channel.is_enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="渠道已禁用，无法连接"
            )
        
        # 更新状态为连接中
        self.repository.update_status(channel_id, ChannelStatus.CONNECTING)
        
        try:
            # TODO: 实现具体的连接逻辑
            # 这里应该调用对应平台的连接器
            
            # 模拟连接成功
            self.repository.update_status(channel_id, ChannelStatus.CONNECTED)
            
            logger.info("渠道连接成功", channel_id=channel_id)
            return {
                "success": True,
                "status": ChannelStatus.CONNECTED,
                "message": "渠道连接成功"
            }
            
        except Exception as e:
            # 连接失败，更新状态
            error_msg = str(e)
            self.repository.update_status(channel_id, ChannelStatus.ERROR, error_msg)
            
            logger.error("渠道连接失败", channel_id=channel_id, error=error_msg)
            return {
                "success": False,
                "status": ChannelStatus.ERROR,
                "message": "渠道连接失败",
                "error": error_msg
            }
    
    async def disconnect_channel(self, channel_id: UUID) -> Dict[str, Any]:
        """断开渠道连接"""
        logger.info("断开渠道连接", channel_id=channel_id)
        
        channel = self.repository.get_by_id(channel_id)
        if not channel:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="渠道不存在"
            )
        
        try:
            # TODO: 实现具体的断开连接逻辑
            
            # 更新状态为已断开
            self.repository.update_status(channel_id, ChannelStatus.DISCONNECTED)
            
            logger.info("渠道断开成功", channel_id=channel_id)
            return {
                "success": True,
                "status": ChannelStatus.DISCONNECTED,
                "message": "渠道断开成功"
            }
            
        except Exception as e:
            error_msg = str(e)
            logger.error("渠道断开失败", channel_id=channel_id, error=error_msg)
            return {
                "success": False,
                "message": "渠道断开失败",
                "error": error_msg
            }
    
    async def auth_xianyu_channel(self, request: XianyuAuthRequest) -> XianyuAuthResponse:
        """闲鱼渠道授权"""
        logger.info("闲鱼渠道授权")
        
        try:
            # TODO: 实现闲鱼授权逻辑
            # 1. 验证cookies有效性
            # 2. 获取用户信息
            # 3. 创建或更新渠道
            
            # 模拟授权成功
            user_info = {
                "user_id": "mock_user_id",
                "nickname": "测试用户",
                "avatar": "https://example.com/avatar.jpg"
            }
            
            # 创建渠道
            channel_request = ChannelCreateRequest(
                name=f"闲鱼_{user_info['nickname']}",
                type=ChannelType.XIANYU,
                platform_account_id=user_info["user_id"],
                platform_account_name=user_info["nickname"],
                credentials={
                    "cookies": request.cookies,
                    "user_agent": request.user_agent,
                    "device_id": request.device_id
                }
            )
            
            channel_response = await self.create_channel(channel_request)
            
            logger.info("闲鱼授权成功", channel_id=channel_response.id)
            return XianyuAuthResponse(
                success=True,
                channel_id=channel_response.id,
                user_info=user_info
            )
            
        except Exception as e:
            error_msg = str(e)
            logger.error("闲鱼授权失败", error=error_msg)
            return XianyuAuthResponse(
                success=False,
                error=error_msg
            )
