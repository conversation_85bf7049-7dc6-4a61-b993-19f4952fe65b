"""
通知服务
处理系统通知和用户通知
"""

import asyncio
import json
from datetime import datetime
from typing import Any, Dict, List, Optional
from uuid import UUID, uuid4

import structlog
from pydantic import BaseModel

from chaiguanjia.core.message_queue import MessageQueueManager, MessageEvent

logger = structlog.get_logger(__name__)


class NotificationData(BaseModel):
    """通知数据模型"""
    id: UUID
    user_id: UUID
    title: str
    content: str
    notification_type: str  # info, warning, error, success
    priority: str  # low, medium, high, urgent
    channels: List[str]  # email, sms, push, in_app
    metadata: Optional[Dict[str, Any]] = None
    created_at: datetime


class NotificationService:
    """通知服务类"""
    
    def __init__(self, message_queue: MessageQueueManager):
        self.message_queue = message_queue
        self.logger = logger.bind(service="notification_service")
    
    async def send_notification(self, notification_data: NotificationData) -> bool:
        """
        发送通知
        
        Args:
            notification_data: 通知数据
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 创建通知事件
            event = MessageEvent(
                event_type="notification.send",
                data={
                    "notification_id": str(notification_data.id),
                    "user_id": str(notification_data.user_id),
                    "title": notification_data.title,
                    "content": notification_data.content,
                    "notification_type": notification_data.notification_type,
                    "priority": notification_data.priority,
                    "channels": notification_data.channels,
                    "metadata": notification_data.metadata or {}
                },
                correlation_id=str(uuid4())
            )
            
            # 发布事件
            await self.message_queue.publish_event(
                event=event,
                routing_key=f"notification.send.{notification_data.user_id}"
            )
            
            self.logger.info(
                "通知发送事件已发布",
                notification_id=str(notification_data.id),
                user_id=str(notification_data.user_id),
                channels=notification_data.channels
            )
            
            return True
            
        except Exception as e:
            self.logger.error(
                "发送通知失败",
                notification_id=str(notification_data.id),
                error=str(e)
            )
            return False
    
    async def send_system_notification(
        self,
        title: str,
        content: str,
        notification_type: str = "info",
        priority: str = "medium",
        channels: Optional[List[str]] = None
    ) -> bool:
        """
        发送系统通知（广播给所有用户）
        
        Args:
            title: 通知标题
            content: 通知内容
            notification_type: 通知类型
            priority: 优先级
            channels: 发送渠道
            
        Returns:
            bool: 发送是否成功
        """
        try:
            if channels is None:
                channels = ["in_app"]
            
            # 创建系统通知事件
            event = MessageEvent(
                event_type="notification.system",
                data={
                    "notification_id": str(uuid4()),
                    "title": title,
                    "content": content,
                    "notification_type": notification_type,
                    "priority": priority,
                    "channels": channels,
                    "created_at": datetime.utcnow().isoformat()
                },
                correlation_id=str(uuid4())
            )
            
            # 发布系统通知事件
            await self.message_queue.publish_event(
                event=event,
                routing_key="notification.system.broadcast"
            )
            
            self.logger.info(
                "系统通知事件已发布",
                title=title,
                notification_type=notification_type,
                channels=channels
            )
            
            return True
            
        except Exception as e:
            self.logger.error(
                "发送系统通知失败",
                title=title,
                error=str(e)
            )
            return False
    
    async def send_user_notification(
        self,
        user_id: UUID,
        title: str,
        content: str,
        notification_type: str = "info",
        priority: str = "medium",
        channels: Optional[List[str]] = None
    ) -> bool:
        """
        发送用户通知
        
        Args:
            user_id: 用户ID
            title: 通知标题
            content: 通知内容
            notification_type: 通知类型
            priority: 优先级
            channels: 发送渠道
            
        Returns:
            bool: 发送是否成功
        """
        if channels is None:
            channels = ["in_app"]
        
        notification_data = NotificationData(
            id=uuid4(),
            user_id=user_id,
            title=title,
            content=content,
            notification_type=notification_type,
            priority=priority,
            channels=channels,
            created_at=datetime.utcnow()
        )
        
        return await self.send_notification(notification_data)
    
    async def handle_notification_sent(self, event_data: Dict[str, Any]) -> None:
        """
        处理通知发送成功事件
        
        Args:
            event_data: 事件数据
        """
        try:
            notification_id = event_data.get("notification_id")
            user_id = event_data.get("user_id")
            channels = event_data.get("channels", [])
            
            self.logger.info(
                "通知发送成功",
                notification_id=notification_id,
                user_id=user_id,
                channels=channels
            )
            
            # TODO: 更新数据库中的通知状态
            # await self.update_notification_status(notification_id, "sent")
            
        except Exception as e:
            self.logger.error(
                "处理通知发送成功事件失败",
                event_data=event_data,
                error=str(e)
            )
    
    async def handle_notification_failed(self, event_data: Dict[str, Any]) -> None:
        """
        处理通知发送失败事件
        
        Args:
            event_data: 事件数据
        """
        try:
            notification_id = event_data.get("notification_id")
            user_id = event_data.get("user_id")
            error_message = event_data.get("error")
            
            self.logger.error(
                "通知发送失败",
                notification_id=notification_id,
                user_id=user_id,
                error=error_message
            )
            
            # TODO: 更新数据库中的通知状态
            # await self.update_notification_status(notification_id, "failed", error=error_message)
            
        except Exception as e:
            self.logger.error(
                "处理通知发送失败事件失败",
                event_data=event_data,
                error=str(e)
            )
    
    async def start_event_listeners(self) -> None:
        """启动事件监听器"""
        try:
            # 监听通知发送成功事件
            await self.message_queue.subscribe_event(
                event_type="notification.sent",
                handler=self.handle_notification_sent,
                routing_key="notification.sent.*"
            )
            
            # 监听通知发送失败事件
            await self.message_queue.subscribe_event(
                event_type="notification.failed",
                handler=self.handle_notification_failed,
                routing_key="notification.failed.*"
            )
            
            self.logger.info("通知服务事件监听器已启动")
            
        except Exception as e:
            self.logger.error(
                "启动通知服务事件监听器失败",
                error=str(e)
            )
    
    async def stop_event_listeners(self) -> None:
        """停止事件监听器"""
        try:
            # TODO: 实现停止监听器的逻辑
            self.logger.info("通知服务事件监听器已停止")
            
        except Exception as e:
            self.logger.error(
                "停止通知服务事件监听器失败",
                error=str(e)
            )
