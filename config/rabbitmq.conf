# RabbitMQ 配置文件
# 柴管家项目专用配置

# 基础配置
listeners.tcp.default = 5672
management.tcp.port = 15672

# 内存和磁盘限制
vm_memory_high_watermark.relative = 0.6
disk_free_limit.relative = 2.0

# 日志配置
log.console = true
log.console.level = info
log.file = /var/log/rabbitmq/rabbit.log
log.file.level = info

# 集群配置（单机模式）
cluster_formation.peer_discovery_backend = classic_config

# 默认用户配置（开发环境）
default_user = chaiguanjia
default_pass = chaiguanjia123

# 默认虚拟主机
default_vhost = chaiguanjia_vhost

# 插件配置
management.load_definitions = /etc/rabbitmq/definitions.json

# 性能优化
channel_max = 2047
frame_max = 131072
heartbeat = 60

# 队列配置
queue_master_locator = min-masters

# 消息持久化
disk_free_limit.absolute = 1GB
