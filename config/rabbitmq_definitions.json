{"rabbit_version": "3.12.0", "rabbitmq_version": "3.12.0", "product_name": "RabbitMQ", "product_version": "3.12.0", "users": [{"name": "chaiguanjia", "password_hash": "gqM9a6HGhQqKMTOLiLhzgw==", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": "administrator"}], "vhosts": [{"name": "chaiguanjia_vhost"}], "permissions": [{"user": "chaiguanjia", "vhost": "chaiguanjia_vhost", "configure": ".*", "write": ".*", "read": ".*"}], "topic_permissions": [], "parameters": [], "global_parameters": [{"name": "cluster_name", "value": "chaiguanjia_cluster"}], "policies": [{"vhost": "chaiguanjia_vhost", "name": "ha-all", "pattern": ".*", "apply-to": "all", "definition": {"ha-mode": "all", "ha-sync-mode": "automatic"}, "priority": 0}], "exchanges": [{"name": "chaiguanjia.events", "vhost": "chaiguanjia_vhost", "type": "topic", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "chaiguanjia.commands", "vhost": "chaiguanjia_vhost", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}, {"name": "chaiguanjia.dlx", "vhost": "chaiguanjia_vhost", "type": "direct", "durable": true, "auto_delete": false, "internal": false, "arguments": {}}], "queues": [{"name": "channel.events", "vhost": "chaiguanjia_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-dead-letter-exchange": "chaiguanjia.dlx", "x-dead-letter-routing-key": "channel.events.dlq"}}, {"name": "message.events", "vhost": "chaiguanjia_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-dead-letter-exchange": "chaiguanjia.dlx", "x-dead-letter-routing-key": "message.events.dlq"}}, {"name": "ai.events", "vhost": "chaiguanjia_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-dead-letter-exchange": "chaiguanjia.dlx", "x-dead-letter-routing-key": "ai.events.dlq"}}, {"name": "user.events", "vhost": "chaiguanjia_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 3600000, "x-dead-letter-exchange": "chaiguanjia.dlx", "x-dead-letter-routing-key": "user.events.dlq"}}, {"name": "channel.commands", "vhost": "chaiguanjia_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 300000, "x-dead-letter-exchange": "chaiguanjia.dlx", "x-dead-letter-routing-key": "channel.commands.dlq"}}, {"name": "message.commands", "vhost": "chaiguanjia_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 300000, "x-dead-letter-exchange": "chaiguanjia.dlx", "x-dead-letter-routing-key": "message.commands.dlq"}}, {"name": "ai.commands", "vhost": "chaiguanjia_vhost", "durable": true, "auto_delete": false, "arguments": {"x-message-ttl": 300000, "x-dead-letter-exchange": "chaiguanjia.dlx", "x-dead-letter-routing-key": "ai.commands.dlq"}}, {"name": "dead_letter_queue", "vhost": "chaiguanjia_vhost", "durable": true, "auto_delete": false, "arguments": {}}], "bindings": [{"source": "chaiguanjia.events", "vhost": "chaiguanjia_vhost", "destination": "channel.events", "destination_type": "queue", "routing_key": "channel.*", "arguments": {}}, {"source": "chaiguanjia.events", "vhost": "chaiguanjia_vhost", "destination": "message.events", "destination_type": "queue", "routing_key": "message.*", "arguments": {}}, {"source": "chaiguanjia.events", "vhost": "chaiguanjia_vhost", "destination": "ai.events", "destination_type": "queue", "routing_key": "ai.*", "arguments": {}}, {"source": "chaiguanjia.events", "vhost": "chaiguanjia_vhost", "destination": "user.events", "destination_type": "queue", "routing_key": "user.*", "arguments": {}}, {"source": "chaiguanjia.commands", "vhost": "chaiguanjia_vhost", "destination": "channel.commands", "destination_type": "queue", "routing_key": "channel.commands", "arguments": {}}, {"source": "chaiguanjia.commands", "vhost": "chaiguanjia_vhost", "destination": "message.commands", "destination_type": "queue", "routing_key": "message.commands", "arguments": {}}, {"source": "chaiguanjia.commands", "vhost": "chaiguanjia_vhost", "destination": "ai.commands", "destination_type": "queue", "routing_key": "ai.commands", "arguments": {}}, {"source": "chaiguanjia.dlx", "vhost": "chaiguanjia_vhost", "destination": "dead_letter_queue", "destination_type": "queue", "routing_key": "*", "arguments": {}}]}