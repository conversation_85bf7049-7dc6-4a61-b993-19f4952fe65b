# 柴管家开发环境 Docker Compose 配置
# 专为开发环境优化，支持热重载和调试

version: '3.8'

services:
  # PostgreSQL 数据库 (开发环境)
  postgres:
    image: postgres:15-alpine
    container_name: chaiguanjia-postgres-dev
    environment:
      POSTGRES_DB: chaiguanjia_dev
      POSTGRES_USER: chaiguanjia
      POSTGRES_PASSWORD: chaiguanjia123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./scripts/init_postgresql.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5433:5432"  # 使用不同端口避免冲突
    networks:
      - chaiguanjia-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chaiguanjia -d chaiguanjia_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # RabbitMQ 消息队列 (开发环境)
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: chaiguanjia-rabbitmq-dev
    environment:
      RABBITMQ_DEFAULT_USER: chaiguanjia
      RABBITMQ_DEFAULT_PASS: chaiguanjia123
      RABBITMQ_DEFAULT_VHOST: chaiguanjia_vhost
    volumes:
      - rabbitmq_dev_data:/var/lib/rabbitmq
      - ./config/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - ./config/rabbitmq_definitions.json:/etc/rabbitmq/definitions.json
    ports:
      - "5673:5672"   # AMQP端口
      - "15673:15672" # 管理界面端口
    networks:
      - chaiguanjia-dev-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis 缓存 (开发环境)
  redis:
    image: redis:7-alpine
    container_name: chaiguanjia-redis-dev
    command: redis-server --appendonly yes --requirepass chaiguanjia123
    volumes:
      - redis_dev_data:/data
    ports:
      - "6380:6379"  # 使用不同端口
    networks:
      - chaiguanjia-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # ChromaDB 向量数据库 (开发环境)
  chromadb:
    image: chromadb/chroma:latest
    container_name: chaiguanjia-chromadb-dev
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    volumes:
      - chromadb_dev_data:/chroma/chroma
    ports:
      - "8002:8000"  # 使用不同端口
    networks:
      - chaiguanjia-dev-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 5

  # 后端开发服务 (热重载)
  backend-dev:
    build:
      context: .
      dockerfile: Dockerfile.backend
      target: development  # 如果有多阶段构建
    container_name: chaiguanjia-backend-dev
    environment:
      # 数据库配置
      DATABASE_URL: postgresql+asyncpg://chaiguanjia:chaiguanjia123@postgres:5432/chaiguanjia_dev
      
      # Redis配置
      REDIS_URL: redis://:chaiguanjia123@redis:6379/0
      
      # RabbitMQ配置
      RABBITMQ_URL: amqp://chaiguanjia:chaiguanjia123@rabbitmq:5672/chaiguanjia_vhost
      
      # ChromaDB配置
      CHROMADB_HOST: chromadb
      CHROMADB_PORT: 8000
      
      # 开发环境配置
      APP_ENV: development
      DEBUG: "true"
      LOG_LEVEL: DEBUG
      RELOAD: "true"
      
      # 安全配置
      SECRET_KEY: dev-secret-key-for-development-only
      ACCESS_TOKEN_EXPIRE_MINUTES: 1440  # 24小时，开发环境更长
    volumes:
      # 挂载源代码以支持热重载
      - ./chaiguanjia:/app/chaiguanjia
      - ./src/Host:/app/src/Host
      - ./scripts:/app/scripts
      - ./config:/app/config
      # 开发工具
      - ./tests:/app/tests
      - ./.env:/app/.env
    ports:
      - "8001:8000"  # 使用不同端口
    networks:
      - chaiguanjia-dev-network
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
      chromadb:
        condition: service_healthy
    command: >
      sh -c "
        echo 'Waiting for dependencies...' &&
        sleep 10 &&
        echo 'Starting development server with hot reload...' &&
        uvicorn src.Host.main:app --host 0.0.0.0 --port 8000 --reload --log-level debug
      "
    develop:
      watch:
        - action: sync
          path: ./chaiguanjia
          target: /app/chaiguanjia
        - action: sync
          path: ./src/Host
          target: /app/src/Host
        - action: restart
          path: ./src/Host/requirements.txt

  # 前端开发服务 (Vite开发服务器)
  frontend-dev:
    image: node:18-alpine
    container_name: chaiguanjia-frontend-dev
    working_dir: /app
    environment:
      - NODE_ENV=development
      - VITE_API_BASE_URL=http://localhost:8001
      - VITE_WS_URL=ws://localhost:8001
    volumes:
      - ./frontend:/app
      - frontend_node_modules:/app/node_modules
    ports:
      - "3001:5173"  # Vite默认端口
    networks:
      - chaiguanjia-dev-network
    depends_on:
      - backend-dev
    command: >
      sh -c "
        echo 'Installing dependencies...' &&
        npm install &&
        echo 'Starting Vite development server...' &&
        npm run dev -- --host 0.0.0.0 --port 5173
      "
    develop:
      watch:
        - action: sync
          path: ./frontend/src
          target: /app/src
        - action: sync
          path: ./frontend/public
          target: /app/public
        - action: restart
          path: ./frontend/package.json

# 开发网络
networks:
  chaiguanjia-dev-network:
    driver: bridge
    name: chaiguanjia-dev-network

# 开发数据卷
volumes:
  postgres_dev_data:
    name: chaiguanjia-postgres-dev-data
  rabbitmq_dev_data:
    name: chaiguanjia-rabbitmq-dev-data
  redis_dev_data:
    name: chaiguanjia-redis-dev-data
  chromadb_dev_data:
    name: chaiguanjia-chromadb-dev-data
  frontend_node_modules:
    name: chaiguanjia-frontend-node-modules
