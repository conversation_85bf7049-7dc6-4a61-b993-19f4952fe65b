# 柴管家 Docker Compose 配置
# 开发环境完整服务栈

version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: chaiguanjia-postgres
    environment:
      POSTGRES_DB: chaiguanjia
      POSTGRES_USER: chaiguanjia
      POSTGRES_PASSWORD: chaiguanjia123
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_postgresql.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - chaiguanjia-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U chaiguanjia -d chaiguanjia"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  # RabbitMQ 消息队列
  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: chaiguanjia-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: chaiguanjia
      RABBITMQ_DEFAULT_PASS: chaiguanjia123
      RABBITMQ_DEFAULT_VHOST: chaiguanjia_vhost
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./config/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf
      - ./config/rabbitmq_definitions.json:/etc/rabbitmq/definitions.json
    ports:
      - "5672:5672"   # AMQP端口
      - "15672:15672" # 管理界面端口
    networks:
      - chaiguanjia-network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: chaiguanjia-redis
    command: redis-server --appendonly yes --requirepass chaiguanjia123
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - chaiguanjia-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped

  # ChromaDB 向量数据库
  chromadb:
    image: chromadb/chroma:latest
    container_name: chaiguanjia-chromadb
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    volumes:
      - chromadb_data:/chroma/chroma
    ports:
      - "8001:8000"
    networks:
      - chaiguanjia-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/heartbeat"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  # 后端服务
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: chaiguanjia-backend
    environment:
      # 数据库配置
      DATABASE_URL: postgresql+asyncpg://chaiguanjia:chaiguanjia123@postgres:5432/chaiguanjia
      
      # Redis配置
      REDIS_URL: redis://:chaiguanjia123@redis:6379/0
      
      # RabbitMQ配置
      RABBITMQ_URL: amqp://chaiguanjia:chaiguanjia123@rabbitmq:5672/chaiguanjia_vhost
      
      # ChromaDB配置
      CHROMADB_HOST: chromadb
      CHROMADB_PORT: 8000
      
      # 应用配置
      APP_ENV: development
      DEBUG: "true"
      LOG_LEVEL: INFO
      
      # 安全配置
      SECRET_KEY: dev-secret-key-change-in-production
      ACCESS_TOKEN_EXPIRE_MINUTES: 30
    volumes:
      - ./chaiguanjia:/app/chaiguanjia
      - ./src/Host:/app/src/Host
      - ./scripts:/app/scripts
      - ./config:/app/config
    ports:
      - "8000:8000"
    networks:
      - chaiguanjia-network
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
      chromadb:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped
    develop:
      watch:
        - action: sync
          path: ./chaiguanjia
          target: /app/chaiguanjia
        - action: sync
          path: ./src/Host
          target: /app/src/Host

  # 前端服务
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: chaiguanjia-frontend
    ports:
      - "3000:3000"
    networks:
      - chaiguanjia-network
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    restart: unless-stopped

# 网络配置
networks:
  chaiguanjia-network:
    driver: bridge
    name: chaiguanjia-network

# 数据卷配置
volumes:
  postgres_data:
    name: chaiguanjia-postgres-data
  rabbitmq_data:
    name: chaiguanjia-rabbitmq-data
  redis_data:
    name: chaiguanjia-redis-data
  chromadb_data:
    name: chaiguanjia-chromadb-data
