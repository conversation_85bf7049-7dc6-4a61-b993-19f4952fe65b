# 柴管家模块架构文档

## 模块概览

本文档描述了柴管家系统中所有业务模块的职责、边界和核心功能。每个模块都是一个独立的限界上下文（Bounded Context），遵循领域驱动设计（DDD）原则。

## 业务模块

### 1. ChannelManagement (渠道管理模块)

**负责人**: AI开发团队  
**核心职责**: 管理多平台账号接入、连接状态监控、账号配置管理

**主要功能**:
- 闲鱼账号接入与授权
- 微信账号接入与授权  
- 抖音账号接入与授权
- 账号连接状态监控
- 账号别名设置与管理
- 连接器配置管理

**数据所有权**:
- channels (渠道账号表)
- channel_configs (渠道配置表)
- connection_logs (连接日志表)

**对外API**:
- 创建渠道账号
- 查询渠道列表
- 更新渠道状态
- 删除渠道账号

### 2. MessageWorkbench (消息工作台模块)

**负责人**: AI开发团队  
**核心职责**: 统一消息管理、跨平台消息收发、会话管理

**主要功能**:
- 统一会话列表展示
- 跨平台消息收发
- 实时消息同步
- 会话历史记录
- 消息状态管理

**数据所有权**:
- conversations (会话表)
- messages (消息表)
- message_status (消息状态表)

**对外API**:
- 获取会话列表
- 发送消息
- 获取消息历史
- 标记消息状态

### 3. AIAssistant (AI助手模块)

**负责人**: AI开发团队  
**核心职责**: AI能力集成、知识库管理、智能回复生成

**主要功能**:
- 知识库问答对管理
- 用户意图分析
- AI回复建议生成
- 置信度评估
- 智能托管模式

**数据所有权**:
- knowledge_base (知识库表)
- intent_analysis (意图分析表)
- ai_responses (AI回复表)
- confidence_scores (置信度表)

**对外API**:
- 管理知识库
- 分析用户意图
- 生成回复建议
- 评估回复置信度

### 4. UserManagement (用户管理模块)

**负责人**: AI开发团队  
**核心职责**: 用户认证、权限管理、用户配置

**主要功能**:
- 用户注册与登录
- 权限管理
- 用户配置管理
- 会话管理

**数据所有权**:
- users (用户表)
- user_roles (用户角色表)
- user_sessions (用户会话表)
- user_preferences (用户偏好表)

**对外API**:
- 用户认证
- 权限验证
- 用户配置管理

## 共享构建块

### Shared.Kernel (共享内核)

**职责**: 提供跨模块共享的核心类型、接口和抽象

**包含内容**:
- 基础实体类型
- 值对象定义
- 领域事件接口
- 通用异常类型
- 基础接口定义

### Shared.Infrastructure (共享基础设施)

**职责**: 提供跨模块共享的基础设施实现

**包含内容**:
- 数据库连接管理
- 消息队列客户端
- 日志记录服务
- 配置管理服务
- 缓存服务

## 模块间通信

### 同步通信 (MediatR)
- 用于需要立即响应的跨模块调用
- 主要用于查询操作

### 异步通信 (事件总线)
- 用于模块间状态变更通知
- 主要用于业务事件传播

## 数据隔离策略

每个模块使用独立的数据库Schema：
- `channel_management` Schema - 渠道管理模块
- `message_workbench` Schema - 消息工作台模块  
- `ai_assistant` Schema - AI助手模块
- `user_management` Schema - 用户管理模块

每个模块使用独立的数据库用户，确保数据访问隔离。
