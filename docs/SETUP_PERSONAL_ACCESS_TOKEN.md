# 🔑 Personal Access Token 配置指南

## 📋 概述

由于GitHub App对个人项目的权限支持有限，我们推荐使用Personal Access Token来实现任务自动化功能。

## 🛠️ 配置步骤

### 第一步：创建Personal Access Token

1. **访问GitHub设置**：
   - 打开：https://github.com/settings/tokens
   - 点击"Generate new token" > "Generate new token (classic)"

2. **配置Token信息**：
   - **Note**: `柴管家任务自动化Token`
   - **Expiration**: 建议选择90天或更长
   - **Select scopes**，勾选以下权限：
     - ✅ `repo` (Full control of private repositories)
     - ✅ `project` (Full control of projects)
     - ✅ `read:org` (Read org and team membership)

3. **生成Token**：
   - 点击"Generate token"
   - **立即复制Token**（只显示一次！）

### 第二步：添加到GitHub Secrets

1. **访问仓库设置**：
   - 打开：https://github.com/Amoresdk/chaiguanjia_8.1/settings/secrets/actions

2. **添加Secret**：
   - 点击"New repository secret"
   - **Name**: `CHAIGUANJIA_PAT` ⭐（注意：不能以GITHUB_开头）
   - **Value**: 粘贴刚才复制的Token
   - **点击"Add secret"**

### 第三步：获取项目信息

1. **找到项目编号**：
   - 访问您的项目：https://github.com/users/Amoresdk/projects
   - 点击"柴管家开发任务看板"项目
   - 从URL中获取项目编号（例如：`/users/Amoresdk/projects/1`中的`1`）

2. **检查项目字段**：
   - 确保项目中有"Status"字段
   - Status字段应包含选项：`Todo`, `In Progress`, `Done`

## 🧪 测试配置

完成上述配置后，运行诊断测试：

1. **手动触发诊断**：
   - 访问：https://github.com/Amoresdk/chaiguanjia_8.1/actions/workflows/diagnose-github-app.yml
   - 点击"Run workflow"
   - 输入项目编号（例如：1）
   - 点击"Run workflow"

2. **检查结果**：
   - 查看workflow运行日志
   - 确认所有步骤都成功执行

## ❓ 常见问题

### Q: Token权限不足怎么办？
A: 确保勾选了`repo`和`project`权限，这两个是必需的。

### Q: 如何找到项目编号？
A: 访问项目页面，URL中的数字就是项目编号，例如`/users/Amoresdk/projects/1`中的`1`。

### Q: Status字段配置错误怎么办？
A: 在项目设置中，确保Status字段包含`Todo`, `In Progress`, `Done`三个选项。

## 🔒 安全注意事项

1. **Token保护**：
   - 不要在代码中硬编码Token
   - 定期更新Token
   - 只授予必要的权限

2. **访问控制**：
   - 确保只有授权人员能访问Secrets
   - 定期审查Token使用情况

## 📞 支持

如果遇到问题，请检查：
1. Token权限是否正确
2. 项目编号是否正确
3. Status字段配置是否正确

配置完成后，您的任务自动化系统将能够：
- ✅ 自动选择下一个优先级任务
- ✅ 更新任务状态
- ✅ 同步Issue和Project状态
- ✅ 生成状态报告
