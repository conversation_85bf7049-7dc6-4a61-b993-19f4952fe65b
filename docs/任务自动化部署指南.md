# 柴管家任务自动化部署指南

## 概述

本指南将帮助您部署和配置柴管家项目的GitHub Projects自动化任务管理系统。

## 前置要求

### 1. GitHub App 配置

您需要创建一个GitHub App来提供API访问权限：

1. 访问 GitHub Settings > Developer settings > GitHub Apps
2. 点击 "New GitHub App"
3. 配置以下信息：
   - **App name**: `柴管家任务自动化`
   - **Homepage URL**: 您的项目仓库URL
   - **Webhook**: 暂时禁用

4. 设置权限：
   ```
   Repository permissions:
   - Contents: Read and write
   - Issues: Read and write
   - Pull requests: Read and write
   - Metadata: Read
   
   Organization permissions:
   - Projects: Read and write
   ```

5. 安装App到您的组织/仓库

### 2. 获取认证信息

1. **App ID**: 在GitHub App设置页面可以找到
2. **Private Key**: 生成并下载私钥文件

## 部署步骤

### 步骤 1: 设置仓库密钥

在您的GitHub仓库中设置以下Secrets和Variables：

#### Secrets (Settings > Secrets and variables > Actions > Secrets)
```bash
CHAIGUANJIA_APP_PEM=<您的GitHub App私钥内容>
```

#### Variables (Settings > Secrets and variables > Actions > Variables)
```bash
CHAIGUANJIA_APP_ID=<您的GitHub App ID>
```

### 步骤 2: 配置项目信息

1. 确认您的GitHub Projects信息：
   - 组织名称: `Amoresdk`
   - 项目编号: `1` (在项目URL中可以找到)
   - 项目名称: `柴管家开发任务看板`

2. 确认项目字段配置：
   - 状态字段名称: `Status`
   - 状态选项: `Todo`, `In Progress`, `Done`

### 步骤 3: 部署文件

确保以下文件已正确部署到您的仓库：

```
.github/
├── workflows/
│   └── task-automation.yml
├── project-config.json
└── automation-state/
    └── (自动生成的状态文件)

scripts/
├── start-next-task.sh
├── complete-task.sh
├── sync-status.sh
└── show-status.sh
```

### 步骤 4: 设置脚本权限

确保脚本文件具有执行权限：

```bash
chmod +x scripts/*.sh
```

### 步骤 5: 验证配置

运行状态检查来验证配置：

1. 进入 Actions 页面
2. 选择 "柴管家任务自动化管理" workflow
3. 点击 "Run workflow"
4. 选择 `show-status` 操作
5. 运行并检查输出

## 使用方法

### 手动触发操作

#### 1. 开始下一个任务
```bash
# 在GitHub Actions中
Action: start-next-task
Dry Run: false (实际执行) / true (预览模式)
```

#### 2. 完成指定任务
```bash
# 在GitHub Actions中
Action: complete-task
Issue Number: <Issue编号，如: 18>
Dry Run: false
```

#### 3. 同步状态
```bash
# 在GitHub Actions中
Action: sync-status
Dry Run: false
```

#### 4. 查看状态
```bash
# 在GitHub Actions中
Action: show-status
```

### 自动触发

系统会在以下情况自动触发：

1. **定时触发**: 每小时执行一次状态同步
2. **事件触发**: 当Issue关闭或PR合并时自动同步状态

## 工作流程

### 典型的任务流程

```mermaid
graph TD
    A[开始下一个任务] --> B[从待办列表选择最高优先级任务]
    B --> C[更新状态为进行中]
    C --> D[开发者进行开发工作]
    D --> E[完成开发]
    E --> F[手动完成任务或Issue关闭]
    F --> G[更新状态为已完成]
    G --> H[自动开始下一个任务]
    H --> A
```

### 状态同步流程

```mermaid
graph TD
    A[定时检查] --> B[查找已关闭但未同步的Issues]
    B --> C{有未同步的Issues?}
    C -->|是| D[更新项目状态为已完成]
    C -->|否| E[检查当前任务状态]
    D --> E
    E --> F{当前任务已关闭?}
    F -->|是| G[自动完成当前任务]
    F -->|否| H[保持当前状态]
    G --> I[尝试开始下一个任务]
    H --> J[结束]
    I --> J
```

## 故障排除

### 常见问题

#### 1. 权限错误
```
Error: Resource not accessible by integration
```
**解决方案**: 检查GitHub App权限配置，确保包含Projects的读写权限。

#### 2. 项目未找到
```
Error: Could not resolve to a ProjectV2 with the number 1
```
**解决方案**: 
- 确认项目编号正确
- 确认GitHub App已安装到正确的组织
- 检查项目是否为Projects V2

#### 3. 字段ID错误
```
Error: Field not found
```
**解决方案**:
- 确认状态字段名称为 "Status"
- 确认状态选项名称为 "Todo", "In Progress", "Done"
- 运行 `show-status` 操作检查字段配置

#### 4. 脚本执行权限
```
Error: Permission denied
```
**解决方案**: 确保脚本文件具有执行权限 (`chmod +x scripts/*.sh`)

### 调试模式

使用预览模式来调试问题：

1. 设置 `Dry Run: true`
2. 查看输出日志
3. 检查操作是否符合预期
4. 确认无误后设置 `Dry Run: false` 执行实际操作

### 日志查看

系统会在以下位置记录日志：

- **GitHub Actions日志**: 实时操作日志
- **操作历史**: `.github/automation-state/operations.log`
- **同步日志**: `.github/automation-state/sync.log`
- **当前任务**: `.github/automation-state/current-task.json`

## 高级配置

### 自定义通知

可以在 `.github/project-config.json` 中配置通知：

```json
{
  "notification": {
    "enabled": true,
    "channels": [
      {
        "type": "slack",
        "webhook": "YOUR_SLACK_WEBHOOK_URL"
      }
    ]
  }
}
```

### 调整重试策略

```json
{
  "retry": {
    "max_attempts": 5,
    "delay_seconds": 10,
    "backoff_multiplier": 2
  }
}
```

### 修改定时触发频率

在 `.github/workflows/task-automation.yml` 中修改：

```yaml
schedule:
  - cron: '0 */2 * * *'  # 每2小时执行一次
```

## 安全考虑

1. **最小权限原则**: GitHub App只授予必要的权限
2. **密钥安全**: 私钥存储在GitHub Secrets中，不会暴露在日志中
3. **操作审计**: 所有操作都有详细的日志记录
4. **预览模式**: 支持干运行模式，避免误操作

## 支持

如果遇到问题，请：

1. 检查GitHub Actions日志
2. 查看故障排除部分
3. 运行 `show-status` 操作获取当前状态
4. 在项目仓库中创建Issue报告问题
