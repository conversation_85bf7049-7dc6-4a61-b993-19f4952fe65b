# 依赖目录
node_modules/

# 构建输出
dist/
build/
out/

# 缓存目录
.cache/
.parcel-cache/
.next/
.nuxt/

# 覆盖率报告
coverage/
.nyc_output/

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时数据
pids/
*.pid
*.seed
*.pid.lock

# 临时文件
.tmp/
temp/

# IDE文件
.vscode/
.idea/
*.swp
*.swo

# OS生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 包管理器文件
package-lock.json
yarn.lock
pnpm-lock.yaml

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 静态资源（通常不需要格式化）
public/
static/
assets/

# 第三方库和供应商文件
vendor/
lib/

# 生成的文件
*.min.js
*.min.css
*.bundle.js
*.chunk.js

# 文档生成
docs/
storybook-static/

# 测试相关
__snapshots__/

# 特定文件类型
*.svg
*.png
*.jpg
*.jpeg
*.gif
*.ico
*.woff
*.woff2
*.ttf
*.eot

# 配置文件（某些不需要格式化）
*.config.js
*.config.ts
.eslintrc.js
.prettierrc.js
