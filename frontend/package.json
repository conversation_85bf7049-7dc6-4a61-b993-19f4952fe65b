{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html"}, "dependencies": {"@heroui/react": "^2.8.2", "@heroui/theme": "^2.4.20", "@hookform/resolvers": "^5.2.1", "@tanstack/react-query": "^5.84.1", "axios": "^1.11.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "lucide-react": "^0.536.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.62.0", "react-router-dom": "^7.7.1", "tailwind-merge": "^3.3.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.6.0", "@vitejs/plugin-react-swc": "^3.11.0", "autoprefixer": "^10.4.21", "babel-jest": "^29.7.0", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-junit": "^16.0.0", "jest-watch-typeahead": "^2.2.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "ts-jest": "^29.2.5", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}