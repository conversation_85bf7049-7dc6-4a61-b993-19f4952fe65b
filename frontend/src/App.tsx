import React from 'react'
import { <PERSON><PERSON><PERSON>rovider } from '@heroui/react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// 页面组件
import Dashboard from '@/pages/Dashboard'
import Channels from '@/pages/Channels'
import Messages from '@/pages/Messages'
import AIAssistant from '@/pages/AIAssistant'
import Settings from '@/pages/Settings'

// 布局组件
import Layout from '@/components/layout/Layout'

// 创建 React Query 客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
})

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <HeroUIProvider>
        <Router>
          <div className="min-h-screen bg-background">
            <Layout>
              <Routes>
                <Route path="/" element={<Dashboard />} />
                <Route path="/channels" element={<Channels />} />
                <Route path="/messages" element={<Messages />} />
                <Route path="/ai-assistant" element={<AIAssistant />} />
                <Route path="/settings" element={<Settings />} />
              </Routes>
            </Layout>
          </div>
        </Router>
      </HeroUIProvider>
    </QueryClientProvider>
  )
}

export default App
