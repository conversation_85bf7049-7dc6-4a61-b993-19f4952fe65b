import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  useDisclosure
} from '@heroui/react';
import { AlertTriangle, Info, CheckCircle, XCircle } from 'lucide-react';

export interface ConfirmDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  message: string;
  type?: 'info' | 'warning' | 'danger' | 'success';
  confirmText?: string;
  cancelText?: string;
  isLoading?: boolean;
}

const iconMap = {
  info: Info,
  warning: AlertTriangle,
  danger: XCircle,
  success: CheckCircle
};

const colorMap = {
  info: 'primary',
  warning: 'warning',
  danger: 'danger',
  success: 'success'
} as const;

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  type = 'info',
  confirmText = '确认',
  cancelText = '取消',
  isLoading = false
}) => {
  const Icon = iconMap[type];
  const color = colorMap[type];

  const handleConfirm = () => {
    onConfirm();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      placement="center"
      backdrop="opaque"
      classNames={{
        backdrop: "bg-gradient-to-t from-zinc-900 to-zinc-900/10 backdrop-opacity-20"
      }}
    >
      <ModalContent>
        <ModalHeader className="flex gap-3 items-center">
          <Icon className={`h-5 w-5 text-${color}`} />
          <span>{title}</span>
        </ModalHeader>
        
        <ModalBody>
          <p className="text-foreground-600">
            {message}
          </p>
        </ModalBody>
        
        <ModalFooter>
          <Button
            color="default"
            variant="light"
            onPress={onClose}
            isDisabled={isLoading}
          >
            {cancelText}
          </Button>
          <Button
            color={color}
            onPress={handleConfirm}
            isLoading={isLoading}
          >
            {confirmText}
          </Button>
        </ModalFooter>
      </ModalContent>
    </Modal>
  );
};

// Hook for easier usage
export const useConfirmDialog = () => {
  const { isOpen, onOpen, onClose } = useDisclosure();
  const [dialogProps, setDialogProps] = React.useState<Omit<ConfirmDialogProps, 'isOpen' | 'onClose'>>({
    onConfirm: () => {},
    title: '',
    message: ''
  });

  const showConfirm = React.useCallback((props: Omit<ConfirmDialogProps, 'isOpen' | 'onClose'>) => {
    setDialogProps(props);
    onOpen();
  }, [onOpen]);

  const ConfirmDialogComponent = React.useCallback(() => (
    <ConfirmDialog
      {...dialogProps}
      isOpen={isOpen}
      onClose={onClose}
    />
  ), [dialogProps, isOpen, onClose]);

  return {
    showConfirm,
    ConfirmDialog: ConfirmDialogComponent
  };
};

export default ConfirmDialog;
