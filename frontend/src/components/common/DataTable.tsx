import React from 'react';
import {
  Table,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Pagination,
  Spinner,
  Card,
  CardBody,
  Button,
  Input,
  Dropdown,
  DropdownTrigger,
  DropdownMenu,
  DropdownItem
} from '@heroui/react';
import { Search, Filter, MoreVertical } from 'lucide-react';

export interface Column<T = any> {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  render?: (value: any, item: T) => React.ReactNode;
}

export interface DataTableProps<T = any> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  pagination?: {
    page: number;
    total: number;
    pageSize: number;
    onPageChange: (page: number) => void;
  };
  searchable?: boolean;
  searchValue?: string;
  onSearchChange?: (value: string) => void;
  searchPlaceholder?: string;
  actions?: {
    label: string;
    key: string;
    onAction: (item: T) => void;
  }[];
  emptyContent?: React.ReactNode;
  className?: string;
}

export const DataTable = <T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  pagination,
  searchable = false,
  searchValue = '',
  onSearchChange,
  searchPlaceholder = '搜索...',
  actions,
  emptyContent = '暂无数据',
  className = ''
}: DataTableProps<T>) => {
  const renderCell = React.useCallback((item: T, columnKey: string) => {
    const column = columns.find(col => col.key === columnKey);
    const cellValue = item[columnKey];

    if (column?.render) {
      return column.render(cellValue, item);
    }

    return cellValue;
  }, [columns]);

  const topContent = React.useMemo(() => {
    if (!searchable && !actions) return null;

    return (
      <div className="flex justify-between gap-3 items-end mb-4">
        {searchable && (
          <Input
            isClearable
            className="w-full sm:max-w-[44%]"
            placeholder={searchPlaceholder}
            startContent={<Search className="h-4 w-4" />}
            value={searchValue}
            onClear={() => onSearchChange?.('')}
            onValueChange={onSearchChange}
          />
        )}
        
        {actions && (
          <div className="flex gap-3">
            <Dropdown>
              <DropdownTrigger className="hidden sm:flex">
                <Button
                  endContent={<Filter className="h-4 w-4" />}
                  variant="flat"
                >
                  操作
                </Button>
              </DropdownTrigger>
              <DropdownMenu
                disallowEmptySelection
                aria-label="Table Actions"
                closeOnSelect={false}
              >
                {actions.map((action) => (
                  <DropdownItem key={action.key}>
                    {action.label}
                  </DropdownItem>
                ))}
              </DropdownMenu>
            </Dropdown>
          </div>
        )}
      </div>
    );
  }, [searchable, searchValue, searchPlaceholder, onSearchChange, actions]);

  const bottomContent = React.useMemo(() => {
    if (!pagination) return null;

    return (
      <div className="py-2 px-2 flex justify-between items-center">
        <span className="w-[30%] text-small text-default-400">
          共 {pagination.total} 条记录
        </span>
        <Pagination
          isCompact
          showControls
          showShadow
          color="primary"
          page={pagination.page}
          total={Math.ceil(pagination.total / pagination.pageSize)}
          onChange={pagination.onPageChange}
        />
        <div className="hidden sm:flex w-[30%] justify-end gap-2">
          <span className="text-default-400 text-small">
            每页 {pagination.pageSize} 条
          </span>
        </div>
      </div>
    );
  }, [pagination]);

  return (
    <Card className={className}>
      <CardBody className="p-0">
        {topContent}
        
        <Table
          aria-label="数据表格"
          isHeaderSticky
          bottomContent={bottomContent}
          bottomContentPlacement="outside"
          classNames={{
            wrapper: "max-h-[382px]",
          }}
        >
          <TableHeader columns={columns}>
            {(column) => (
              <TableColumn
                key={column.key}
                width={column.width}
                allowsSorting={column.sortable}
              >
                {column.label}
              </TableColumn>
            )}
          </TableHeader>
          
          <TableBody
            items={data}
            loadingContent={<Spinner label="加载中..." />}
            loadingState={loading ? "loading" : "idle"}
            emptyContent={emptyContent}
          >
            {(item) => (
              <TableRow key={item.id || Math.random()}>
                {(columnKey) => (
                  <TableCell>
                    {renderCell(item, columnKey as string)}
                  </TableCell>
                )}
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardBody>
    </Card>
  );
};

export default DataTable;
