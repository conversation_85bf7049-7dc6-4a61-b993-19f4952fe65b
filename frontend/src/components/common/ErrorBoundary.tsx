import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Card, CardBody, Button } from '@heroui/react';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // 记录错误到日志服务
    console.error('ErrorBoundary caught an error:', error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="flex items-center justify-center min-h-[400px] p-4">
          <Card className="max-w-md w-full">
            <CardBody className="text-center space-y-4">
              <div className="flex justify-center">
                <AlertTriangle className="h-12 w-12 text-danger" />
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-foreground">
                  出现了一些问题
                </h3>
                <p className="text-sm text-foreground-500 mt-2">
                  页面遇到了意外错误，请尝试刷新页面或联系技术支持。
                </p>
              </div>

              {process.env.NODE_ENV === 'development' && this.state.error && (
                <div className="text-left">
                  <details className="text-xs">
                    <summary className="cursor-pointer text-danger">
                      错误详情 (开发模式)
                    </summary>
                    <pre className="mt-2 p-2 bg-default-100 rounded text-xs overflow-auto">
                      {this.state.error.toString()}
                      {this.state.errorInfo?.componentStack}
                    </pre>
                  </details>
                </div>
              )}

              <div className="flex gap-2 justify-center">
                <Button
                  color="primary"
                  variant="solid"
                  startContent={<RefreshCw className="h-4 w-4" />}
                  onPress={this.handleRetry}
                >
                  重试
                </Button>
                <Button
                  color="default"
                  variant="bordered"
                  onPress={() => window.location.reload()}
                >
                  刷新页面
                </Button>
              </div>
            </CardBody>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
