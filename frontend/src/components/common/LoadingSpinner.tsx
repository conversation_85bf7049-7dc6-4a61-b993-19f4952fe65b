import React from 'react';
import { Spinner } from '@heroui/react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  label?: string;
  className?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  label = '加载中...',
  className = ''
}) => {
  return (
    <div className={`flex flex-col items-center justify-center p-4 ${className}`}>
      <Spinner
        size={size}
        color={color}
        label={label}
      />
    </div>
  );
};

export default LoadingSpinner;
