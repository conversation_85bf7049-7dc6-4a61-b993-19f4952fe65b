# 柴管家开发脚本

本目录包含了柴管家项目的各种开发和运维脚本。

## 脚本列表

### 🚀 环境管理

#### `start_dev_environment.sh`
启动完整的开发环境，包括所有必要的服务。

```bash
# 启动开发环境
./scripts/start_dev_environment.sh

# 停止开发环境
./scripts/start_dev_environment.sh stop

# 查看服务状态
./scripts/start_dev_environment.sh status
```

**功能:**
- 检查系统依赖 (Docker, Node.js, Python)
- 启动基础设施服务 (PostgreSQL, RabbitMQ, Redis, ChromaDB)
- 初始化数据库
- 安装前端和后端依赖
- 启动后端API服务 (端口8000)
- 启动前端开发服务器 (端口3000)

#### `health_check.sh`
快速检查所有服务的健康状态。

```bash
./scripts/health_check.sh
```

**检查项目:**
- Docker服务状态
- 数据库容器状态
- 网络服务可访问性
- 应用进程状态

#### `verify_environment.py`
全面的环境验证脚本，检查开发环境是否正确配置。

```bash
python scripts/verify_environment.py
```

**验证项目:**
- Python环境和依赖包
- Node.js环境和前端依赖
- Docker环境
- 数据库连接
- 消息队列连接
- 向量数据库连接
- 后端API可用性
- 前端应用可用性
- CI/CD配置文件

### 🗄️ 数据库管理

#### `setup_database.sh`
初始化PostgreSQL数据库。

```bash
./scripts/setup_database.sh
```

#### `setup_chromadb.py`
初始化ChromaDB向量数据库。

```bash
python scripts/setup_chromadb.py
```

### 🐰 消息队列管理

#### `setup_rabbitmq.sh`
配置RabbitMQ交换机和队列。

```bash
./scripts/setup_rabbitmq.sh
```

### 🐳 Docker管理

#### `docker_utils.sh`
Docker相关的实用工具。

```bash
# 构建所有镜像
./scripts/docker_utils.sh build

# 清理Docker资源
./scripts/docker_utils.sh clean

# 查看日志
./scripts/docker_utils.sh logs [service_name]
```

### 🔍 代码质量

#### `check_code_quality.sh`
运行代码质量检查。

```bash
./scripts/check_code_quality.sh
```

**检查项目:**
- Python代码格式化 (Black)
- Python导入排序 (isort)
- Python代码检查 (flake8, mypy)
- Python安全检查 (bandit)
- 前端代码检查 (ESLint)
- 前端代码格式化 (Prettier)
- 单元测试
- 测试覆盖率

## 快速开始

### 首次设置

1. **克隆项目并进入目录**
   ```bash
   git clone <repository-url>
   cd chaiguanjia_8.1
   ```

2. **运行环境验证**
   ```bash
   python scripts/verify_environment.py
   ```

3. **启动开发环境**
   ```bash
   ./scripts/start_dev_environment.sh
   ```

4. **验证服务状态**
   ```bash
   ./scripts/health_check.sh
   ```

### 日常开发

1. **启动开发环境**
   ```bash
   ./scripts/start_dev_environment.sh
   ```

2. **检查代码质量**
   ```bash
   ./scripts/check_code_quality.sh
   ```

3. **停止开发环境**
   ```bash
   ./scripts/start_dev_environment.sh stop
   ```

## 服务端口

| 服务 | 端口 | 描述 |
|------|------|------|
| 前端应用 | 3000 | React开发服务器 |
| 后端API | 8000 | FastAPI应用 |
| PostgreSQL | 5432 | 主数据库 |
| RabbitMQ | 5672 | 消息队列 |
| RabbitMQ管理 | 15672 | Web管理界面 |
| Redis | 6379 | 缓存数据库 |
| ChromaDB | 8000 | 向量数据库 |

## 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   lsof -i :8000
   
   # 杀死进程
   kill -9 <PID>
   ```

2. **Docker服务启动失败**
   ```bash
   # 查看Docker日志
   docker compose -f docker-compose.dev.yml logs
   
   # 重新启动服务
   docker compose -f docker-compose.dev.yml down
   docker compose -f docker-compose.dev.yml up -d
   ```

3. **数据库连接失败**
   ```bash
   # 检查PostgreSQL状态
   docker compose -f docker-compose.dev.yml ps postgres
   
   # 查看数据库日志
   docker compose -f docker-compose.dev.yml logs postgres
   ```

4. **前端依赖问题**
   ```bash
   # 清理并重新安装
   cd frontend
   rm -rf node_modules package-lock.json
   npm install
   ```

5. **Python依赖问题**
   ```bash
   # 重新创建虚拟环境
   rm -rf venv
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

### 获取帮助

如果遇到问题，请按以下顺序排查：

1. 运行健康检查: `./scripts/health_check.sh`
2. 查看服务日志: `docker compose -f docker-compose.dev.yml logs`
3. 检查环境配置: `python scripts/verify_environment.py`
4. 查看项目文档: `docs/`

## 贡献指南

在提交代码前，请确保：

1. 运行代码质量检查: `./scripts/check_code_quality.sh`
2. 运行所有测试: `npm test` (前端) 和 `pytest` (后端)
3. 更新相关文档

---

**注意**: 所有脚本都应该在项目根目录下运行。
