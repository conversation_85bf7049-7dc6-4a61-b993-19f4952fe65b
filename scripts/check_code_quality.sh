#!/bin/bash

# 代码质量检查脚本
# 用于本地开发和CI/CD流水线中的代码质量检查

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python代码质量
check_python_quality() {
    log_info "检查Python代码质量..."
    
    # 检查是否安装了必要的工具
    if ! command -v black &> /dev/null; then
        log_error "Black未安装，请运行: pip install black"
        return 1
    fi
    
    if ! command -v isort &> /dev/null; then
        log_error "isort未安装，请运行: pip install isort"
        return 1
    fi
    
    if ! command -v flake8 &> /dev/null; then
        log_error "flake8未安装，请运行: pip install flake8"
        return 1
    fi
    
    if ! command -v mypy &> /dev/null; then
        log_error "mypy未安装，请运行: pip install mypy"
        return 1
    fi
    
    # Black代码格式检查
    log_info "运行Black代码格式检查..."
    if black --check --diff chaiguanjia/ src/Host/; then
        log_success "Black检查通过"
    else
        log_error "Black检查失败，请运行: black chaiguanjia/ src/Host/"
        return 1
    fi
    
    # isort导入排序检查
    log_info "运行isort导入排序检查..."
    if isort --check-only --diff chaiguanjia/ src/Host/; then
        log_success "isort检查通过"
    else
        log_error "isort检查失败，请运行: isort chaiguanjia/ src/Host/"
        return 1
    fi
    
    # flake8代码风格检查
    log_info "运行flake8代码风格检查..."
    if flake8 chaiguanjia/ src/Host/ --max-line-length=88 --extend-ignore=E203,W503; then
        log_success "flake8检查通过"
    else
        log_error "flake8检查失败，请修复代码风格问题"
        return 1
    fi
    
    # mypy类型检查
    log_info "运行mypy类型检查..."
    if mypy chaiguanjia/ --ignore-missing-imports; then
        log_success "mypy检查通过"
    else
        log_error "mypy检查失败，请修复类型问题"
        return 1
    fi
    
    log_success "Python代码质量检查全部通过"
}

# 检查前端代码质量
check_frontend_quality() {
    log_info "检查前端代码质量..."
    
    cd frontend
    
    # 检查是否安装了依赖
    if [ ! -d "node_modules" ]; then
        log_warning "前端依赖未安装，正在安装..."
        npm ci
    fi
    
    # ESLint检查
    log_info "运行ESLint检查..."
    if npm run lint; then
        log_success "ESLint检查通过"
    else
        log_error "ESLint检查失败，请运行: npm run lint:fix"
        cd ..
        return 1
    fi
    
    # Prettier格式检查
    log_info "运行Prettier格式检查..."
    if npm run format:check; then
        log_success "Prettier检查通过"
    else
        log_error "Prettier检查失败，请运行: npm run format"
        cd ..
        return 1
    fi
    
    # TypeScript类型检查
    log_info "运行TypeScript类型检查..."
    if npm run type-check; then
        log_success "TypeScript检查通过"
    else
        log_error "TypeScript检查失败，请修复类型错误"
        cd ..
        return 1
    fi
    
    cd ..
    log_success "前端代码质量检查全部通过"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    # Python测试
    log_info "运行Python测试..."
    if command -v pytest &> /dev/null; then
        if pytest tests/ -v --cov=chaiguanjia --cov-report=term-missing; then
            log_success "Python测试通过"
        else
            log_error "Python测试失败"
            return 1
        fi
    else
        log_warning "pytest未安装，跳过Python测试"
    fi
    
    # 前端测试
    log_info "运行前端测试..."
    cd frontend
    if npm run test:ci; then
        log_success "前端测试通过"
    else
        log_error "前端测试失败"
        cd ..
        return 1
    fi
    cd ..
    
    log_success "所有测试通过"
}

# 安全检查
security_check() {
    log_info "运行安全检查..."
    
    # Python安全检查
    if command -v bandit &> /dev/null; then
        log_info "运行bandit安全检查..."
        if bandit -r chaiguanjia/ src/Host/ -ll; then
            log_success "bandit安全检查通过"
        else
            log_warning "bandit发现潜在安全问题"
        fi
    else
        log_warning "bandit未安装，跳过Python安全检查"
    fi
    
    if command -v safety &> /dev/null; then
        log_info "运行safety依赖安全检查..."
        if safety check; then
            log_success "safety检查通过"
        else
            log_warning "safety发现安全漏洞"
        fi
    else
        log_warning "safety未安装，跳过依赖安全检查"
    fi
    
    # 前端安全检查
    log_info "运行前端依赖审计..."
    cd frontend
    if npm audit --audit-level=moderate; then
        log_success "前端依赖审计通过"
    else
        log_warning "前端依赖存在安全问题"
    fi
    cd ..
}

# 主函数
main() {
    local check_type=${1:-all}
    local exit_code=0
    
    log_info "开始代码质量检查..."
    
    case $check_type in
        "python")
            check_python_quality || exit_code=1
            ;;
        "frontend")
            check_frontend_quality || exit_code=1
            ;;
        "test")
            run_tests || exit_code=1
            ;;
        "security")
            security_check || exit_code=1
            ;;
        "all")
            check_python_quality || exit_code=1
            check_frontend_quality || exit_code=1
            run_tests || exit_code=1
            security_check || exit_code=1
            ;;
        *)
            log_error "未知的检查类型: $check_type"
            log_info "可用选项: python, frontend, test, security, all"
            exit 1
            ;;
    esac
    
    if [ $exit_code -eq 0 ]; then
        log_success "🎉 所有代码质量检查通过！"
    else
        log_error "❌ 代码质量检查失败，请修复问题后重试"
    fi
    
    exit $exit_code
}

# 显示帮助信息
show_help() {
    echo "代码质量检查脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  python      只检查Python代码质量"
    echo "  frontend    只检查前端代码质量"
    echo "  test        只运行测试"
    echo "  security    只运行安全检查"
    echo "  all         运行所有检查 (默认)"
    echo "  help        显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0              # 运行所有检查"
    echo "  $0 python       # 只检查Python代码"
    echo "  $0 frontend     # 只检查前端代码"
}

# 检查参数
if [ "$1" = "help" ] || [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    show_help
    exit 0
fi

# 运行主函数
main "$@"
