#!/bin/bash

# 柴管家任务自动化 - 完成任务
# 功能：将指定的任务标记为已完成

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
if [ $# -eq 0 ] && [ -z "$ISSUE_NUMBER" ]; then
    log_error "请提供Issue编号"
    log_info "用法: $0 <issue_number>"
    log_info "或设置环境变量: ISSUE_NUMBER=<issue_number>"
    exit 1
fi

# 获取Issue编号
ISSUE_NUMBER=${1:-$ISSUE_NUMBER}

# 检查必要的环境变量
check_environment() {
    local required_vars=("PROJECT_ID" "STATUS_FIELD_ID" "DONE_OPTION_ID" "GH_TOKEN")
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "缺少必要的环境变量: $var"
            exit 1
        fi
    done
}

# 获取当前任务信息
get_current_task() {
    local current_task_file=".github/automation-state/current-task.json"
    
    if [ -f "$current_task_file" ]; then
        cat "$current_task_file"
    else
        echo "null"
    fi
}

# 清除当前任务信息
clear_current_task() {
    local current_task_file=".github/automation-state/current-task.json"
    
    if [ -f "$current_task_file" ]; then
        rm "$current_task_file"
    fi
}

# 查找项目中的Issue
find_issue_in_project() {
    local issue_number="$1"
    
    log_info "在项目中查找Issue #$issue_number..."
    
    local query='
    query($projectId: ID!) {
      node(id: $projectId) {
        ... on ProjectV2 {
          items(first: 100) {
            nodes {
              id
              content {
                ... on Issue {
                  id
                  number
                  title
                  url
                  state
                }
              }
              fieldValues(first: 10) {
                nodes {
                  ... on ProjectV2ItemFieldSingleSelectValue {
                    field {
                      ... on ProjectV2SingleSelectField {
                        id
                        name
                      }
                    }
                    optionId
                  }
                }
              }
            }
          }
        }
      }
    }'
    
    local all_items=$(gh api graphql -f query="$query" -f projectId="$PROJECT_ID")
    
    echo "$all_items" | jq --arg issue_num "$issue_number" '
    .data.node.items.nodes[] | 
    select(.content != null and (.content.number | tostring) == $issue_num) |
    {
      itemId: .id,
      issueId: .content.id,
      issueNumber: .content.number,
      title: .content.title,
      url: .content.url,
      state: .content.state,
      currentStatus: (.fieldValues.nodes[] | select(.field.name == "Status") | .optionId)
    }'
}

# 更新任务状态为已完成
update_task_to_done() {
    local item_id="$1"
    local task_title="$2"
    
    log_info "将任务标记为已完成: $task_title"
    
    if [ "$DRY_RUN" = "true" ]; then
        log_warning "[预览模式] 将更新任务状态为已完成"
        return 0
    fi
    
    local mutation='
    mutation($projectId: ID!, $itemId: ID!, $fieldId: ID!, $optionId: String!) {
      updateProjectV2ItemFieldValue(input: {
        projectId: $projectId
        itemId: $itemId
        fieldId: $fieldId
        value: { singleSelectOptionId: $optionId }
      }) {
        projectV2Item {
          id
          content {
            ... on Issue {
              number
              title
            }
          }
        }
      }
    }'
    
    local result=$(gh api graphql \
        -f query="$mutation" \
        -f projectId="$PROJECT_ID" \
        -f itemId="$item_id" \
        -f fieldId="$STATUS_FIELD_ID" \
        -f optionId="$DONE_OPTION_ID")
    
    if echo "$result" | jq -e '.data.updateProjectV2ItemFieldValue' > /dev/null; then
        log_success "任务状态更新为已完成"
        return 0
    else
        log_error "任务状态更新失败"
        echo "$result" | jq '.errors // .'
        return 1
    fi
}

# 记录操作日志
log_operation() {
    local operation="$1"
    local task_info="$2"
    local status="$3"
    
    local log_file=".github/automation-state/operations.log"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    mkdir -p "$(dirname "$log_file")"
    
    local log_entry=$(jq -n \
        --arg timestamp "$timestamp" \
        --arg operation "$operation" \
        --arg status "$status" \
        --argjson task "$task_info" \
        '{
          timestamp: $timestamp,
          operation: $operation,
          status: $status,
          task: $task
        }')
    
    echo "$log_entry" >> "$log_file"
}

# 主函数
main() {
    log_info "开始完成任务流程: Issue #$ISSUE_NUMBER"
    
    # 检查环境
    check_environment
    
    # 查找Issue
    local task_info=$(find_issue_in_project "$ISSUE_NUMBER")
    
    if [ -z "$task_info" ] || [ "$task_info" = "" ]; then
        log_error "在项目中未找到Issue #$ISSUE_NUMBER"
        exit 1
    fi
    
    # 解析任务信息
    local item_id=$(echo "$task_info" | jq -r '.itemId')
    local title=$(echo "$task_info" | jq -r '.title')
    local url=$(echo "$task_info" | jq -r '.url')
    local current_status=$(echo "$task_info" | jq -r '.currentStatus')
    
    log_info "找到任务:"
    log_info "  Issue #$ISSUE_NUMBER: $title"
    log_info "  URL: $url"
    
    # 检查当前状态
    if [ "$current_status" = "$DONE_OPTION_ID" ]; then
        log_warning "任务已经是已完成状态"
        exit 0
    fi
    
    # 更新任务状态为已完成
    if update_task_to_done "$item_id" "$title"; then
        # 如果这是当前正在进行的任务，清除当前任务记录
        local current_task=$(get_current_task)
        if [ "$current_task" != "null" ] && [ -n "$current_task" ]; then
            # 检查是否为有效的JSON
            if echo "$current_task" | jq . > /dev/null 2>&1; then
                local current_issue_number=$(echo "$current_task" | jq -r '.issueNumber // empty')
                if [ "$current_issue_number" = "$ISSUE_NUMBER" ]; then
                    if [ "$DRY_RUN" != "true" ]; then
                        clear_current_task
                        log_info "已清除当前任务记录"
                    fi
                fi
            fi
        fi
        
        # 记录操作日志
        log_operation "complete-task" "$task_info" "success"
        
        log_success "任务已完成: #$ISSUE_NUMBER - $title"
        
        # 输出GitHub Actions摘要
        if [ -n "$GITHUB_STEP_SUMMARY" ]; then
            cat >> "$GITHUB_STEP_SUMMARY" << EOF
## ✅ 任务已完成

**Issue #$ISSUE_NUMBER**: $title

**状态**: 进行中 → 已完成

**任务链接**: [$url]($url)

**完成时间**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
EOF
        fi
        
        # 自动开始下一个任务（如果没有其他正在进行的任务）
        log_info "检查是否可以自动开始下一个任务..."
        if [ "$DRY_RUN" != "true" ]; then
            bash scripts/start-next-task.sh || log_warning "无法自动开始下一个任务"
        fi
        
    else
        log_operation "complete-task" "$task_info" "failed"
        log_error "任务完成失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
