#!/bin/bash

# Docker开发环境管理脚本
# 用于管理柴管家项目的Docker开发环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker是否安装
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi

    log_success "Docker 环境检查通过"
}

# 构建镜像
build_images() {
    log_info "构建 Docker 镜像..."
    
    # 构建后端镜像
    log_info "构建后端镜像..."
    docker build -f Dockerfile.backend -t chaiguanjia-backend:dev .
    
    # 构建前端镜像
    log_info "构建前端镜像..."
    docker build -f Dockerfile.frontend -t chaiguanjia-frontend:dev .
    
    log_success "镜像构建完成"
}

# 启动开发环境
start_dev() {
    log_info "启动开发环境..."
    
    # 创建网络（如果不存在）
    docker network create chaiguanjia-dev-network 2>/dev/null || true
    
    # 启动服务
    docker-compose -f docker-compose.dev.yml up -d
    
    log_success "开发环境启动完成"
    log_info "服务访问地址:"
    log_info "  前端: http://localhost:3001"
    log_info "  后端: http://localhost:8001"
    log_info "  后端API文档: http://localhost:8001/docs"
    log_info "  RabbitMQ管理界面: http://localhost:15673"
    log_info "  PostgreSQL: localhost:5433"
    log_info "  Redis: localhost:6380"
    log_info "  ChromaDB: http://localhost:8002"
}

# 停止开发环境
stop_dev() {
    log_info "停止开发环境..."
    docker-compose -f docker-compose.dev.yml down
    log_success "开发环境已停止"
}

# 重启开发环境
restart_dev() {
    log_info "重启开发环境..."
    stop_dev
    start_dev
}

# 查看日志
logs() {
    local service=${1:-}
    
    if [ -z "$service" ]; then
        log_info "查看所有服务日志..."
        docker-compose -f docker-compose.dev.yml logs -f
    else
        log_info "查看 $service 服务日志..."
        docker-compose -f docker-compose.dev.yml logs -f "$service"
    fi
}

# 进入容器
exec_container() {
    local service=${1:-backend-dev}
    local shell=${2:-bash}
    
    log_info "进入 $service 容器..."
    docker-compose -f docker-compose.dev.yml exec "$service" "$shell"
}

# 清理环境
clean() {
    log_warning "这将删除所有开发环境的容器、网络和数据卷"
    read -p "确定要继续吗? (y/N): " -n 1 -r
    echo
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "清理开发环境..."
        
        # 停止并删除容器
        docker-compose -f docker-compose.dev.yml down -v --remove-orphans
        
        # 删除镜像
        docker rmi chaiguanjia-backend:dev chaiguanjia-frontend:dev 2>/dev/null || true
        
        # 删除网络
        docker network rm chaiguanjia-dev-network 2>/dev/null || true
        
        log_success "开发环境清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 健康检查
health_check() {
    log_info "检查服务健康状态..."
    
    services=("postgres" "rabbitmq" "redis" "chromadb" "backend-dev" "frontend-dev")
    
    for service in "${services[@]}"; do
        status=$(docker-compose -f docker-compose.dev.yml ps -q "$service" | xargs docker inspect --format='{{.State.Health.Status}}' 2>/dev/null || echo "not_running")
        
        case $status in
            "healthy")
                log_success "$service: 健康"
                ;;
            "unhealthy")
                log_error "$service: 不健康"
                ;;
            "starting")
                log_warning "$service: 启动中"
                ;;
            "not_running")
                log_error "$service: 未运行"
                ;;
            *)
                log_warning "$service: 状态未知 ($status)"
                ;;
        esac
    done
}

# 数据库操作
db_operations() {
    local operation=${1:-}
    
    case $operation in
        "migrate")
            log_info "运行数据库迁移..."
            docker-compose -f docker-compose.dev.yml exec backend-dev python -m alembic upgrade head
            ;;
        "reset")
            log_warning "这将重置数据库，所有数据将丢失"
            read -p "确定要继续吗? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                docker-compose -f docker-compose.dev.yml exec backend-dev python scripts/reset_database.py
                log_success "数据库重置完成"
            fi
            ;;
        "backup")
            log_info "备份数据库..."
            timestamp=$(date +%Y%m%d_%H%M%S)
            docker-compose -f docker-compose.dev.yml exec postgres pg_dump -U chaiguanjia chaiguanjia_dev > "backup_${timestamp}.sql"
            log_success "数据库备份完成: backup_${timestamp}.sql"
            ;;
        *)
            log_error "未知的数据库操作: $operation"
            log_info "可用操作: migrate, reset, backup"
            ;;
    esac
}

# 显示帮助信息
show_help() {
    echo "柴管家 Docker 开发环境管理脚本"
    echo ""
    echo "用法: $0 <命令> [参数]"
    echo ""
    echo "命令:"
    echo "  build           构建 Docker 镜像"
    echo "  start           启动开发环境"
    echo "  stop            停止开发环境"
    echo "  restart         重启开发环境"
    echo "  logs [service]  查看日志 (可选指定服务名)"
    echo "  exec [service]  进入容器 (默认: backend-dev)"
    echo "  health          检查服务健康状态"
    echo "  clean           清理开发环境"
    echo "  db <operation>  数据库操作 (migrate/reset/backup)"
    echo "  help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start                    # 启动开发环境"
    echo "  $0 logs backend-dev         # 查看后端日志"
    echo "  $0 exec frontend-dev sh     # 进入前端容器"
    echo "  $0 db migrate               # 运行数据库迁移"
}

# 主函数
main() {
    local command=${1:-help}
    
    case $command in
        "build")
            check_docker
            build_images
            ;;
        "start")
            check_docker
            start_dev
            ;;
        "stop")
            stop_dev
            ;;
        "restart")
            check_docker
            restart_dev
            ;;
        "logs")
            logs "$2"
            ;;
        "exec")
            exec_container "$2" "$3"
            ;;
        "health")
            health_check
            ;;
        "clean")
            clean
            ;;
        "db")
            db_operations "$2"
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
