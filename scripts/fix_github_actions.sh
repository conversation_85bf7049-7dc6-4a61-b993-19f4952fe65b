#!/bin/bash

# GitHub Actions 修复脚本
# 提交工作流文件并验证配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Git状态
check_git_status() {
    log_info "检查Git仓库状态..."
    
    if ! git rev-parse --git-dir > /dev/null 2>&1; then
        log_error "当前目录不是Git仓库"
        exit 1
    fi
    
    # 检查是否有远程仓库
    if ! git remote get-url origin > /dev/null 2>&1; then
        log_error "没有配置远程仓库origin"
        exit 1
    fi
    
    log_success "Git仓库状态正常"
}

# 验证工作流文件语法
validate_workflows() {
    log_info "验证GitHub Actions工作流语法..."
    
    if ! command -v yamllint &> /dev/null; then
        log_warning "yamllint未安装，跳过YAML语法检查"
        log_info "建议安装: pip install yamllint"
    else
        for workflow in .github/workflows/*.yml; do
            if [ -f "$workflow" ]; then
                log_info "检查 $workflow..."
                if yamllint "$workflow"; then
                    log_success "$workflow 语法正确"
                else
                    log_error "$workflow 语法错误"
                    exit 1
                fi
            fi
        done
    fi
}

# 提交工作流文件
commit_workflows() {
    log_info "提交GitHub Actions工作流文件..."
    
    # 检查是否有未跟踪的.github目录
    if git status --porcelain | grep -q "^?? .github/"; then
        log_info "添加.github目录到Git..."
        git add .github/
    fi
    
    # 检查是否有其他重要文件需要提交
    important_files=(
        ".gitignore"
        "README.md"
        "pyproject.toml"
        "docker-compose.yml"
        "docker-compose.dev.yml"
        "Dockerfile.backend"
        "Dockerfile.frontend"
        ".env.example"
    )
    
    for file in "${important_files[@]}"; do
        if [ -f "$file" ] && git status --porcelain | grep -q "^?? $file"; then
            log_info "添加 $file 到Git..."
            git add "$file"
        fi
    done
    
    # 检查是否有变更需要提交
    if git diff --cached --quiet; then
        log_warning "没有新的变更需要提交"
    else
        log_info "提交变更..."
        git commit -m "feat: 添加GitHub Actions CI/CD工作流配置

- 添加持续集成流水线 (ci.yml)
- 添加代码质量检查工作流 (code-quality.yml)
- 添加依赖更新自动化 (dependency-update.yml)
- 添加发布自动化工作流 (release.yml)
- 配置Docker构建和部署
- 设置代码覆盖率检查
- 添加安全扫描和依赖审计

Closes #1"
        
        log_success "变更已提交"
    fi
}

# 推送到远程仓库
push_to_remote() {
    log_info "推送到远程仓库..."
    
    current_branch=$(git branch --show-current)
    log_info "当前分支: $current_branch"
    
    # 推送到远程
    if git push origin "$current_branch"; then
        log_success "成功推送到远程仓库"
    else
        log_error "推送失败，请检查网络连接和权限"
        exit 1
    fi
}

# 验证GitHub Actions状态
verify_github_actions() {
    log_info "验证GitHub Actions配置..."
    
    remote_url=$(git remote get-url origin)
    repo_url=${remote_url%.git}
    repo_url=${repo_url#**************:}
    repo_url=${repo_url#https://github.com/}
    
    actions_url="https://github.com/$repo_url/actions"
    
    echo ""
    log_success "GitHub Actions配置已完成！"
    echo ""
    echo "📋 下一步操作:"
    echo "1. 访问GitHub Actions页面: $actions_url"
    echo "2. 等待1-2分钟让GitHub处理新的工作流"
    echo "3. 如果没有自动触发，可以手动触发工作流"
    echo ""
    echo "🔧 手动触发方法:"
    echo "1. 在GitHub仓库页面，点击 'Actions' 标签"
    echo "2. 选择 'CI/CD Pipeline' 工作流"
    echo "3. 点击 'Run workflow' 按钮"
    echo "4. 选择分支并点击 'Run workflow'"
    echo ""
    echo "📊 工作流说明:"
    echo "- CI/CD Pipeline: 主要的持续集成流水线"
    echo "- Code Quality: 代码质量检查"
    echo "- Dependency Update: 依赖更新自动化"
    echo "- Release: 发布自动化"
}

# 创建测试提交来触发工作流
create_test_commit() {
    log_info "创建测试提交来触发工作流..."
    
    # 创建一个简单的测试文件
    echo "# GitHub Actions 测试" > .github-actions-test.md
    echo "此文件用于测试GitHub Actions工作流是否正常工作。" >> .github-actions-test.md
    echo "创建时间: $(date)" >> .github-actions-test.md
    
    git add .github-actions-test.md
    git commit -m "test: 触发GitHub Actions工作流测试

- 添加测试文件以验证CI/CD流水线
- 验证工作流触发条件是否正确配置"
    
    git push origin "$(git branch --show-current)"
    
    log_success "测试提交已推送，应该会触发GitHub Actions工作流"
}

# 主函数
main() {
    echo -e "${BLUE}=== GitHub Actions 修复工具 ===${NC}"
    echo ""
    
    check_git_status
    validate_workflows
    commit_workflows
    push_to_remote
    verify_github_actions
    
    echo ""
    read -p "是否创建测试提交来触发工作流？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        create_test_commit
    fi
    
    echo ""
    log_success "🎉 GitHub Actions修复完成！"
}

# 运行主函数
main "$@"
