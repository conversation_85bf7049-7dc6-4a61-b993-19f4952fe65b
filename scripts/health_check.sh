#!/bin/bash

# 健康检查脚本
# 快速检查所有服务是否正常运行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查结果
CHECKS_PASSED=0
TOTAL_CHECKS=0

# 检查函数
check_service() {
    local service_name="$1"
    local check_command="$2"
    local expected_result="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "检查 $service_name... "
    
    if eval "$check_command" > /dev/null 2>&1; then
        echo -e "${GREEN}✓${NC}"
        CHECKS_PASSED=$((CHECKS_PASSED + 1))
        return 0
    else
        echo -e "${RED}✗${NC}"
        return 1
    fi
}

# 检查HTTP服务
check_http_service() {
    local service_name="$1"
    local url="$2"
    local expected_status="${3:-200}"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    
    echo -n "检查 $service_name ($url)... "
    
    if curl -s -o /dev/null -w "%{http_code}" "$url" | grep -q "$expected_status"; then
        echo -e "${GREEN}✓${NC}"
        CHECKS_PASSED=$((CHECKS_PASSED + 1))
        return 0
    else
        echo -e "${RED}✗${NC}"
        return 1
    fi
}

echo -e "${BLUE}=== 柴管家健康检查 ===${NC}"
echo ""

# 检查Docker服务
echo -e "${YELLOW}Docker服务:${NC}"
check_service "Docker" "docker info"
check_service "PostgreSQL容器" "docker compose -f docker-compose.dev.yml ps postgres | grep -q 'Up'"
check_service "RabbitMQ容器" "docker compose -f docker-compose.dev.yml ps rabbitmq | grep -q 'Up'"
check_service "Redis容器" "docker compose -f docker-compose.dev.yml ps redis | grep -q 'Up'"
check_service "ChromaDB容器" "docker compose -f docker-compose.dev.yml ps chromadb | grep -q 'Up'"

echo ""

# 检查网络服务
echo -e "${YELLOW}网络服务:${NC}"
check_http_service "后端API健康检查" "http://localhost:8000/health"
check_http_service "后端API文档" "http://localhost:8000/docs"
check_http_service "前端应用" "http://localhost:3000"
check_http_service "RabbitMQ管理界面" "http://localhost:15672"

echo ""

# 检查数据库连接
echo -e "${YELLOW}数据库连接:${NC}"
check_service "PostgreSQL连接" "docker exec \$(docker compose -f docker-compose.dev.yml ps -q postgres) pg_isready -U chaiguanjia"

echo ""

# 检查进程
echo -e "${YELLOW}应用进程:${NC}"
if [ -f ".backend.pid" ]; then
    check_service "后端进程" "kill -0 \$(cat .backend.pid)"
else
    echo -n "检查 后端进程... "
    echo -e "${YELLOW}PID文件不存在${NC}"
fi

if [ -f ".frontend.pid" ]; then
    check_service "前端进程" "kill -0 \$(cat .frontend.pid)"
else
    echo -n "检查 前端进程... "
    echo -e "${YELLOW}PID文件不存在${NC}"
fi

echo ""

# 显示结果
echo -e "${BLUE}=== 检查结果 ===${NC}"
if [ $CHECKS_PASSED -eq $TOTAL_CHECKS ]; then
    echo -e "${GREEN}✅ 所有检查通过 ($CHECKS_PASSED/$TOTAL_CHECKS)${NC}"
    echo -e "${GREEN}🎉 开发环境运行正常！${NC}"
    exit 0
else
    echo -e "${RED}❌ 部分检查失败 ($CHECKS_PASSED/$TOTAL_CHECKS)${NC}"
    echo -e "${YELLOW}⚠️  请检查失败的服务并重新启动${NC}"
    exit 1
fi
