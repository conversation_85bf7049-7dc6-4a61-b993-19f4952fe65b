#!/usr/bin/env python3
"""
ChromaDB 初始化脚本
创建默认集合和配置向量数据库
"""

import os
import sys
import chromadb
from chromadb.config import Settings
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def init_chromadb():
    """初始化 ChromaDB"""
    
    # 获取数据库路径
    db_path = os.getenv('CHROMA_DB_PATH', './data/chromadb')
    
    # 确保目录存在
    os.makedirs(db_path, exist_ok=True)
    
    logger.info(f"初始化 ChromaDB，数据路径: {db_path}")
    
    try:
        # 创建 ChromaDB 客户端
        client = chromadb.PersistentClient(
            path=db_path,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=True
            )
        )
        
        # 定义默认集合
        collections = [
            {
                "name": "knowledge_base",
                "metadata": {
                    "description": "知识库问答对",
                    "hnsw:space": "cosine"
                }
            },
            {
                "name": "conversation_history",
                "metadata": {
                    "description": "会话历史记录",
                    "hnsw:space": "cosine"
                }
            },
            {
                "name": "user_intents",
                "metadata": {
                    "description": "用户意图分析",
                    "hnsw:space": "cosine"
                }
            },
            {
                "name": "ai_responses",
                "metadata": {
                    "description": "AI回复模板",
                    "hnsw:space": "cosine"
                }
            }
        ]
        
        # 创建集合
        for collection_info in collections:
            try:
                collection = client.get_or_create_collection(
                    name=collection_info["name"],
                    metadata=collection_info["metadata"]
                )
                logger.info(f"✓ 创建集合: {collection_info['name']}")
                
                # 获取集合信息
                count = collection.count()
                logger.info(f"  - 文档数量: {count}")
                
            except Exception as e:
                logger.error(f"✗ 创建集合失败 {collection_info['name']}: {e}")
                return False
        
        # 添加一些示例数据到知识库
        knowledge_collection = client.get_collection("knowledge_base")
        
        sample_data = [
            {
                "id": "sample_1",
                "document": "问：你们的课程价格是多少？\n答：我们的课程价格根据不同类型有所不同，基础课程299元，进阶课程599元，高级课程999元。",
                "metadata": {
                    "category": "pricing",
                    "tags": ["价格", "课程"],
                    "confidence": 0.95
                }
            },
            {
                "id": "sample_2", 
                "document": "问：如何报名课程？\n答：您可以通过我们的官网、微信小程序或者直接联系客服进行报名。报名后会有专门的学习顾问为您服务。",
                "metadata": {
                    "category": "enrollment",
                    "tags": ["报名", "流程"],
                    "confidence": 0.90
                }
            },
            {
                "id": "sample_3",
                "document": "问：课程有什么优势？\n答：我们的课程具有以下优势：1.名师授课 2.实战项目 3.一对一辅导 4.就业保障 5.终身学习",
                "metadata": {
                    "category": "features",
                    "tags": ["优势", "特色"],
                    "confidence": 0.88
                }
            }
        ]
        
        # 添加示例数据
        if knowledge_collection.count() == 0:
            knowledge_collection.add(
                documents=[item["document"] for item in sample_data],
                metadatas=[item["metadata"] for item in sample_data],
                ids=[item["id"] for item in sample_data]
            )
            logger.info(f"✓ 添加示例知识库数据: {len(sample_data)} 条")
        
        logger.info("ChromaDB 初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"ChromaDB 初始化失败: {e}")
        return False

def main():
    """主函数"""
    success = init_chromadb()
    if success:
        logger.info("✓ ChromaDB 初始化成功")
        sys.exit(0)
    else:
        logger.error("✗ ChromaDB 初始化失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
