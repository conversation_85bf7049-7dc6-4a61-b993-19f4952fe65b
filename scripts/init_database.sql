-- 柴管家数据库初始化脚本 (PostgreSQL)
-- 创建数据库、用户、Schema和表结构

-- 创建数据库（需要以超级用户身份执行）
-- CREATE DATABASE chaiguanjia WITH ENCODING 'UTF8' LC_COLLATE='zh_CN.UTF-8' LC_CTYPE='zh_CN.UTF-8';

-- 连接到数据库
\c chaiguanjia;

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建模块专用的Schema
CREATE SCHEMA IF NOT EXISTS user_management;
CREATE SCHEMA IF NOT EXISTS channel_management;
CREATE SCHEMA IF NOT EXISTS message_workbench;
CREATE SCHEMA IF NOT EXISTS ai_assistant;

-- ===== 用户管理模块表 =====

-- 创建用户角色枚举类型
CREATE TYPE user_role AS ENUM ('admin', 'user');

-- 用户表
CREATE TABLE IF NOT EXISTS user_management.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    role user_role DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_users_username ON user_management.users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON user_management.users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON user_management.users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON user_management.users(is_active);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为用户表创建更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON user_management.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_management.sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    access_token VARCHAR(500) NOT NULL,
    refresh_token VARCHAR(500) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (user_id) REFERENCES user_management.users(id) ON DELETE CASCADE
);

-- 创建会话表索引
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_management.sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_access_token ON user_management.sessions(access_token);
CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON user_management.sessions(expires_at);

-- ===== 渠道管理模块表 =====

-- 渠道表
CREATE TABLE IF NOT EXISTS channel_management_channels (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,
    type ENUM('wechat', 'qq', 'douyin', 'xiaohongshu', 'weibo') NOT NULL,
    status ENUM('connected', 'disconnected', 'error') DEFAULT 'disconnected',
    config JSON NOT NULL,
    last_sync_at TIMESTAMP NULL,
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES user_management_users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_last_sync_at (last_sync_at)
);

-- 渠道连接日志表
CREATE TABLE IF NOT EXISTS channel_management_connection_logs (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    channel_id VARCHAR(36) NOT NULL,
    action ENUM('connect', 'disconnect', 'sync', 'error') NOT NULL,
    status ENUM('success', 'failed') NOT NULL,
    message TEXT,
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (channel_id) REFERENCES channel_management_channels(id) ON DELETE CASCADE,
    INDEX idx_channel_id (channel_id),
    INDEX idx_action (action),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- ===== 消息工作台模块表 =====

-- 会话表
CREATE TABLE IF NOT EXISTS message_workbench_conversations (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    channel_id VARCHAR(36) NOT NULL,
    participant_id VARCHAR(100) NOT NULL,
    participant_name VARCHAR(100) NOT NULL,
    participant_avatar VARCHAR(500),
    unread_count INT DEFAULT 0,
    status ENUM('active', 'archived', 'blocked') DEFAULT 'active',
    last_message_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (channel_id) REFERENCES channel_management_channels(id) ON DELETE CASCADE,
    UNIQUE KEY uk_channel_participant (channel_id, participant_id),
    INDEX idx_channel_id (channel_id),
    INDEX idx_participant_id (participant_id),
    INDEX idx_status (status),
    INDEX idx_last_message_at (last_message_at)
);

-- 消息表
CREATE TABLE IF NOT EXISTS message_workbench_messages (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    conversation_id VARCHAR(36) NOT NULL,
    sender_id VARCHAR(100) NOT NULL,
    sender_name VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    message_type ENUM('text', 'image', 'video', 'audio', 'file') DEFAULT 'text',
    direction ENUM('inbound', 'outbound') NOT NULL,
    status ENUM('sent', 'delivered', 'read', 'failed') DEFAULT 'sent',
    metadata JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES message_workbench_conversations(id) ON DELETE CASCADE,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_sender_id (sender_id),
    INDEX idx_direction (direction),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- ===== AI助手模块表 =====

-- 知识库表
CREATE TABLE IF NOT EXISTS ai_assistant_knowledge_base (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id VARCHAR(36) NOT NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category VARCHAR(50) DEFAULT 'general',
    tags JSON,
    confidence DECIMAL(3,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INT DEFAULT 0,
    last_used_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES user_management_users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_category (category),
    INDEX idx_is_active (is_active),
    INDEX idx_confidence (confidence),
    INDEX idx_usage_count (usage_count),
    FULLTEXT idx_question_answer (question, answer)
);

-- AI对话历史表
CREATE TABLE IF NOT EXISTS ai_assistant_conversation_history (
    id VARCHAR(36) PRIMARY KEY DEFAULT (UUID()),
    conversation_id VARCHAR(36) NOT NULL,
    user_message TEXT NOT NULL,
    ai_response TEXT,
    intent VARCHAR(100),
    confidence DECIMAL(3,2),
    knowledge_base_ids JSON,
    processing_time_ms INT,
    feedback ENUM('positive', 'negative', 'neutral') NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES message_workbench_conversations(id) ON DELETE CASCADE,
    INDEX idx_conversation_id (conversation_id),
    INDEX idx_intent (intent),
    INDEX idx_confidence (confidence),
    INDEX idx_feedback (feedback),
    INDEX idx_created_at (created_at)
);

-- 创建默认管理员用户
INSERT IGNORE INTO user_management_users (id, username, email, password_hash, role) 
VALUES (
    UUID(),
    'admin',
    '<EMAIL>',
    -- 密码: admin123 (实际部署时应该修改)
    '$2b$12$LQv3c1yqBwEHxPuNYjHNTO.eMQZRVESyqHpSqdmewj2TSRwjkqQ1.',
    'admin'
);

-- 创建索引优化查询性能
-- 复合索引
CREATE INDEX idx_messages_conversation_created ON message_workbench_messages(conversation_id, created_at DESC);
CREATE INDEX idx_conversations_channel_updated ON message_workbench_conversations(channel_id, updated_at DESC);
CREATE INDEX idx_knowledge_category_active ON ai_assistant_knowledge_base(category, is_active, confidence DESC);

COMMIT;
