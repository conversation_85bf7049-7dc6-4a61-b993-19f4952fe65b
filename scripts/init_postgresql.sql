-- 柴管家数据库初始化脚本 (PostgreSQL)
-- 创建数据库、用户、Schema和表结构

-- 连接到数据库
-- \c chaiguanjia;

-- 启用UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建模块专用的Schema
CREATE SCHEMA IF NOT EXISTS user_management;
CREATE SCHEMA IF NOT EXISTS channel_management;
CREATE SCHEMA IF NOT EXISTS message_workbench;
CREATE SCHEMA IF NOT EXISTS ai_assistant;

-- 创建枚举类型
CREATE TYPE user_role AS ENUM ('admin', 'user');
CREATE TYPE channel_type AS ENUM ('wechat', 'qq', 'douyin', 'xiaohongshu', 'weibo');
CREATE TYPE channel_status AS ENUM ('connected', 'disconnected', 'error');
CREATE TYPE connection_action AS ENUM ('connect', 'disconnect', 'sync', 'error');
CREATE TYPE action_status AS ENUM ('success', 'failed');
CREATE TYPE conversation_status AS ENUM ('active', 'archived', 'blocked');
CREATE TYPE message_type AS ENUM ('text', 'image', 'video', 'audio', 'file');
CREATE TYPE message_direction AS ENUM ('inbound', 'outbound');
CREATE TYPE message_status AS ENUM ('sent', 'delivered', 'read', 'failed');
CREATE TYPE feedback_type AS ENUM ('positive', 'negative', 'neutral');

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- ===== 用户管理模块表 =====

-- 用户表
CREATE TABLE IF NOT EXISTS user_management.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    role user_role DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户表索引
CREATE INDEX IF NOT EXISTS idx_users_username ON user_management.users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON user_management.users(email);
CREATE INDEX IF NOT EXISTS idx_users_role ON user_management.users(role);
CREATE INDEX IF NOT EXISTS idx_users_is_active ON user_management.users(is_active);

-- 用户表更新时间触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON user_management.users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_management.sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    access_token VARCHAR(500) NOT NULL,
    refresh_token VARCHAR(500) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES user_management.users(id) ON DELETE CASCADE
);

-- 会话表索引
CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON user_management.sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_sessions_access_token ON user_management.sessions(access_token);
CREATE INDEX IF NOT EXISTS idx_sessions_expires_at ON user_management.sessions(expires_at);

-- ===== 渠道管理模块表 =====

-- 渠道表
CREATE TABLE IF NOT EXISTS channel_management.channels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    type channel_type NOT NULL,
    status channel_status DEFAULT 'disconnected',
    config JSONB NOT NULL,
    last_sync_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES user_management.users(id) ON DELETE CASCADE
);

-- 渠道表索引
CREATE INDEX IF NOT EXISTS idx_channels_user_id ON channel_management.channels(user_id);
CREATE INDEX IF NOT EXISTS idx_channels_type ON channel_management.channels(type);
CREATE INDEX IF NOT EXISTS idx_channels_status ON channel_management.channels(status);
CREATE INDEX IF NOT EXISTS idx_channels_last_sync_at ON channel_management.channels(last_sync_at);

-- 渠道表更新时间触发器
CREATE TRIGGER update_channels_updated_at BEFORE UPDATE ON channel_management.channels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 渠道连接日志表
CREATE TABLE IF NOT EXISTS channel_management.connection_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID NOT NULL,
    action connection_action NOT NULL,
    status action_status NOT NULL,
    message TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (channel_id) REFERENCES channel_management.channels(id) ON DELETE CASCADE
);

-- 连接日志表索引
CREATE INDEX IF NOT EXISTS idx_connection_logs_channel_id ON channel_management.connection_logs(channel_id);
CREATE INDEX IF NOT EXISTS idx_connection_logs_action ON channel_management.connection_logs(action);
CREATE INDEX IF NOT EXISTS idx_connection_logs_status ON channel_management.connection_logs(status);
CREATE INDEX IF NOT EXISTS idx_connection_logs_created_at ON channel_management.connection_logs(created_at);

-- ===== 消息工作台模块表 =====

-- 会话表
CREATE TABLE IF NOT EXISTS message_workbench.conversations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID NOT NULL,
    participant_id VARCHAR(100) NOT NULL,
    participant_name VARCHAR(100) NOT NULL,
    participant_avatar VARCHAR(500),
    unread_count INTEGER DEFAULT 0,
    status conversation_status DEFAULT 'active',
    last_message_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (channel_id) REFERENCES channel_management.channels(id) ON DELETE CASCADE,
    UNIQUE (channel_id, participant_id)
);

-- 会话表索引
CREATE INDEX IF NOT EXISTS idx_conversations_channel_id ON message_workbench.conversations(channel_id);
CREATE INDEX IF NOT EXISTS idx_conversations_participant_id ON message_workbench.conversations(participant_id);
CREATE INDEX IF NOT EXISTS idx_conversations_status ON message_workbench.conversations(status);
CREATE INDEX IF NOT EXISTS idx_conversations_last_message_at ON message_workbench.conversations(last_message_at);

-- 会话表更新时间触发器
CREATE TRIGGER update_conversations_updated_at BEFORE UPDATE ON message_workbench.conversations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 消息表
CREATE TABLE IF NOT EXISTS message_workbench.messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL,
    sender_id VARCHAR(100) NOT NULL,
    sender_name VARCHAR(100) NOT NULL,
    content TEXT NOT NULL,
    message_type message_type DEFAULT 'text',
    direction message_direction NOT NULL,
    status message_status DEFAULT 'sent',
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES message_workbench.conversations(id) ON DELETE CASCADE
);

-- 消息表索引
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON message_workbench.messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON message_workbench.messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_direction ON message_workbench.messages(direction);
CREATE INDEX IF NOT EXISTS idx_messages_status ON message_workbench.messages(status);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON message_workbench.messages(created_at);

-- 消息表更新时间触发器
CREATE TRIGGER update_messages_updated_at BEFORE UPDATE ON message_workbench.messages
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===== AI助手模块表 =====

-- 知识库表
CREATE TABLE IF NOT EXISTS ai_assistant.knowledge_base (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    category VARCHAR(50) DEFAULT 'general',
    tags JSONB,
    confidence DECIMAL(3,2) DEFAULT 0.00,
    is_active BOOLEAN DEFAULT TRUE,
    usage_count INTEGER DEFAULT 0,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES user_management.users(id) ON DELETE CASCADE
);

-- 知识库表索引
CREATE INDEX IF NOT EXISTS idx_knowledge_base_user_id ON ai_assistant.knowledge_base(user_id);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_category ON ai_assistant.knowledge_base(category);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_is_active ON ai_assistant.knowledge_base(is_active);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_confidence ON ai_assistant.knowledge_base(confidence);
CREATE INDEX IF NOT EXISTS idx_knowledge_base_usage_count ON ai_assistant.knowledge_base(usage_count);

-- 知识库全文搜索索引
CREATE INDEX IF NOT EXISTS idx_knowledge_base_question_fts ON ai_assistant.knowledge_base 
    USING gin(to_tsvector('chinese', question));
CREATE INDEX IF NOT EXISTS idx_knowledge_base_answer_fts ON ai_assistant.knowledge_base 
    USING gin(to_tsvector('chinese', answer));

-- 知识库表更新时间触发器
CREATE TRIGGER update_knowledge_base_updated_at BEFORE UPDATE ON ai_assistant.knowledge_base
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- AI对话历史表
CREATE TABLE IF NOT EXISTS ai_assistant.conversation_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    conversation_id UUID NOT NULL,
    user_message TEXT NOT NULL,
    ai_response TEXT,
    intent VARCHAR(100),
    confidence DECIMAL(3,2),
    knowledge_base_ids JSONB,
    processing_time_ms INTEGER,
    feedback feedback_type,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (conversation_id) REFERENCES message_workbench.conversations(id) ON DELETE CASCADE
);

-- AI对话历史表索引
CREATE INDEX IF NOT EXISTS idx_conversation_history_conversation_id ON ai_assistant.conversation_history(conversation_id);
CREATE INDEX IF NOT EXISTS idx_conversation_history_intent ON ai_assistant.conversation_history(intent);
CREATE INDEX IF NOT EXISTS idx_conversation_history_confidence ON ai_assistant.conversation_history(confidence);
CREATE INDEX IF NOT EXISTS idx_conversation_history_feedback ON ai_assistant.conversation_history(feedback);
CREATE INDEX IF NOT EXISTS idx_conversation_history_created_at ON ai_assistant.conversation_history(created_at);

-- 复合索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_messages_conversation_created ON message_workbench.messages(conversation_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_conversations_channel_updated ON message_workbench.conversations(channel_id, updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_knowledge_category_active ON ai_assistant.knowledge_base(category, is_active, confidence DESC);

-- 创建默认管理员用户
INSERT INTO user_management.users (username, email, password_hash, role) 
VALUES (
    'admin',
    '<EMAIL>',
    -- 密码: admin123 (实际部署时应该修改)
    '$2b$12$LQv3c1yqBwEHxPuNYjHNTO.eMQZRVESyqHpSqdmewj2TSRwjkqQ1.',
    'admin'
) ON CONFLICT (username) DO NOTHING;

COMMIT;
