#!/usr/bin/env node

/**
 * 柴管家项目自动化管理脚本
 * 功能：自动化Projects看板管理、Sprint规划、进度跟踪
 */

const { Octokit } = require('@octokit/rest');
const octokit = new Octokit({ auth: process.env.GITHUB_TOKEN });

const CONFIG = {
  owner: 'Amoresdk',
  repo: 'chaiguanjia_8.1',
  projectId: 'PVT_kwDOABC123', // 替换为实际项目ID
  sprints: {
    'sprint:1': { name: '渠道接入管理', dueDate: '2025-08-16' },
    'sprint:2': { name: '消息处理工作台', dueDate: '2025-08-30' },
    'sprint:3': { name: 'AI智能助手', dueDate: '2025-09-13' },
    'sprint:4': { name: '人机协作模式', dueDate: '2025-09-27' }
  }
};

class ProjectManager {
  async syncIssuestoProject() {
    console.log('🔄 同步Issues到Projects看板...');
    
    // 获取所有开放的Issues
    const { data: issues } = await octokit.rest.issues.listForRepo({
      owner: CONFIG.owner,
      repo: CONFIG.repo,
      state: 'open'
    });

    for (const issue of issues) {
      await this.processIssue(issue);
    }
  }

  async processIssue(issue) {
    const labels = issue.labels.map(l => l.name);
    
    // 检查Epic分类
    const epic = labels.find(l => l.startsWith('epic:'));
    const sprint = labels.find(l => l.startsWith('sprint:'));
    const priority = labels.find(l => l.startsWith('priority:'));

    console.log(`📋 处理Issue #${issue.number}: ${issue.title}`);
    console.log(`   Epic: ${epic}, Sprint: ${sprint}, Priority: ${priority}`);

    // 添加到Projects（如果还未添加）
    await this.addToProject(issue.id);
    
    // 设置字段值
    await this.updateProjectFields(issue.id, { epic, sprint, priority });
  }

  async addToProject(issueId) {
    // 使用GraphQL API添加到项目
    const mutation = `
      mutation($projectId: ID!, $contentId: ID!) {
        addProjectV2ItemById(input: {projectId: $projectId, contentId: $contentId}) {
          item {
            id
          }
        }
      }
    `;

    try {
      await octokit.graphql(mutation, {
        projectId: CONFIG.projectId,
        contentId: issueId
      });
      console.log(`   ✅ 已添加到Projects`);
    } catch (error) {
      if (!error.message.includes('already exists')) {
        console.error(`   ❌ 添加失败: ${error.message}`);
      }
    }
  }

  async updateProjectFields(issueId, fields) {
    // 更新项目字段的逻辑
    console.log(`   🔄 更新字段: ${JSON.stringify(fields)}`);
  }

  async generateSprintReport() {
    console.log('📊 生成Sprint报告...');
    
    for (const [sprintLabel, sprintInfo] of Object.entries(CONFIG.sprints)) {
      const { data: issues } = await octokit.rest.issues.listForRepo({
        owner: CONFIG.owner,
        repo: CONFIG.repo,
        state: 'all',
        labels: sprintLabel
      });

      const total = issues.length;
      const completed = issues.filter(i => i.state === 'closed').length;
      const inProgress = issues.filter(i => 
        i.labels.some(l => l.name === 'status:in-progress')
      ).length;
      
      const progress = total > 0 ? Math.round((completed / total) * 100) : 0;
      
      console.log(`\n📈 ${sprintInfo.name} (${sprintLabel})`);
      console.log(`   进度: ${progress}% (${completed}/${total})`);
      console.log(`   进行中: ${inProgress}`);
      console.log(`   截止日期: ${sprintInfo.dueDate}`);
      
      // 检查是否需要预警
      const dueDate = new Date(sprintInfo.dueDate);
      const today = new Date();
      const daysLeft = Math.ceil((dueDate - today) / (1000 * 60 * 60 * 24));
      
      if (daysLeft <= 7 && progress < 80) {
        console.log(`   ⚠️  预警: 距离截止还有${daysLeft}天，但进度仅${progress}%`);
      }
    }
  }

  async autoAssignPriority() {
    console.log('🎯 自动分配优先级...');
    
    const { data: issues } = await octokit.rest.issues.listForRepo({
      owner: CONFIG.owner,
      repo: CONFIG.repo,
      state: 'open'
    });

    for (const issue of issues) {
      const labels = issue.labels.map(l => l.name);
      const hasPriority = labels.some(l => l.startsWith('priority:'));
      
      if (!hasPriority) {
        // 基于里程碑和创建时间自动分配优先级
        let priority = 'priority:medium';
        
        if (issue.milestone) {
          const milestone = issue.milestone.title;
          if (milestone.includes('Sprint 1') || milestone.includes('Sprint 2')) {
            priority = 'priority:high';
          }
        }
        
        await octokit.rest.issues.addLabels({
          owner: CONFIG.owner,
          repo: CONFIG.repo,
          issue_number: issue.number,
          labels: [priority]
        });
        
        console.log(`   ✅ Issue #${issue.number} 自动分配优先级: ${priority}`);
      }
    }
  }
}

// 主函数
async function main() {
  const manager = new ProjectManager();
  
  try {
    await manager.syncIssuestoProject();
    await manager.generateSprintReport();
    await manager.autoAssignPriority();
    console.log('\n🎉 项目管理自动化完成！');
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = ProjectManager;
