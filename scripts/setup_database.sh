#!/bin/bash

# 柴管家数据库环境配置脚本
# 用于初始化PostgreSQL和ChromaDB

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装 $1"
        exit 1
    fi
}

# 检查PostgreSQL是否运行
check_postgresql() {
    log_info "检查 PostgreSQL 服务状态..."
    
    if command -v pg_isready &> /dev/null; then
        if pg_isready -q; then
            log_success "PostgreSQL 服务正在运行"
            return 0
        else
            log_error "PostgreSQL 服务未运行，请启动 PostgreSQL"
            return 1
        fi
    else
        log_warning "无法检查 PostgreSQL 状态，假设服务正在运行"
        return 0
    fi
}

# 创建数据库
create_database() {
    log_info "创建数据库 chaiguanjia..."
    
    # 检查数据库是否已存在
    if psql -lqt | cut -d \| -f 1 | grep -qw chaiguanjia; then
        log_warning "数据库 chaiguanjia 已存在"
    else
        createdb chaiguanjia -E UTF8 -l zh_CN.UTF-8
        log_success "数据库 chaiguanjia 创建成功"
    fi
}

# 初始化数据库表结构
init_database_schema() {
    log_info "初始化数据库表结构..."
    
    if [ -f "scripts/init_postgresql.sql" ]; then
        psql -d chaiguanjia -f scripts/init_postgresql.sql
        log_success "数据库表结构初始化完成"
    else
        log_error "找不到数据库初始化脚本 scripts/init_postgresql.sql"
        exit 1
    fi
}

# 初始化ChromaDB
init_chromadb() {
    log_info "初始化 ChromaDB..."
    
    # 检查Python环境
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装 Python3"
        exit 1
    fi
    
    # 检查ChromaDB是否已安装
    if ! python3 -c "import chromadb" &> /dev/null; then
        log_error "ChromaDB 未安装，请运行: pip install chromadb"
        exit 1
    fi
    
    # 运行ChromaDB初始化脚本
    if [ -f "scripts/init_chromadb.py" ]; then
        python3 scripts/init_chromadb.py
        log_success "ChromaDB 初始化完成"
    else
        log_error "找不到 ChromaDB 初始化脚本 scripts/init_chromadb.py"
        exit 1
    fi
}

# 创建数据目录
create_data_directories() {
    log_info "创建数据目录..."
    
    mkdir -p data/chromadb
    mkdir -p data/uploads
    mkdir -p data/logs
    mkdir -p data/backups
    
    log_success "数据目录创建完成"
}

# 设置权限
set_permissions() {
    log_info "设置目录权限..."
    
    chmod 755 data
    chmod 755 data/chromadb
    chmod 755 data/uploads
    chmod 755 data/logs
    chmod 755 data/backups
    
    log_success "目录权限设置完成"
}

# 主函数
main() {
    log_info "开始配置柴管家数据库环境..."
    
    # 检查必要的命令
    check_command "psql"
    check_command "createdb"
    check_command "python3"
    
    # 检查PostgreSQL服务
    if ! check_postgresql; then
        exit 1
    fi
    
    # 创建数据目录
    create_data_directories
    
    # 创建数据库
    create_database
    
    # 初始化数据库表结构
    init_database_schema
    
    # 初始化ChromaDB
    init_chromadb
    
    # 设置权限
    set_permissions
    
    log_success "数据库环境配置完成！"
    log_info "您现在可以启动应用程序了"
}

# 显示帮助信息
show_help() {
    echo "柴管家数据库环境配置脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  --pg-only      仅配置 PostgreSQL"
    echo "  --chroma-only  仅配置 ChromaDB"
    echo ""
    echo "示例:"
    echo "  $0              # 完整配置"
    echo "  $0 --pg-only    # 仅配置 PostgreSQL"
    echo "  $0 --chroma-only # 仅配置 ChromaDB"
}

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    --pg-only)
        log_info "仅配置 PostgreSQL..."
        check_command "psql"
        check_command "createdb"
        check_postgresql || exit 1
        create_data_directories
        create_database
        init_database_schema
        set_permissions
        log_success "PostgreSQL 配置完成！"
        ;;
    --chroma-only)
        log_info "仅配置 ChromaDB..."
        check_command "python3"
        create_data_directories
        init_chromadb
        set_permissions
        log_success "ChromaDB 配置完成！"
        ;;
    "")
        main
        ;;
    *)
        log_error "未知选项: $1"
        show_help
        exit 1
        ;;
esac
