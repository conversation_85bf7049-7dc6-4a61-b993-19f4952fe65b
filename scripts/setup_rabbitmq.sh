#!/bin/bash

# RabbitMQ 设置脚本
# 用于初始化 RabbitMQ 服务器配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 RabbitMQ 是否安装
check_rabbitmq_installation() {
    log_info "检查 RabbitMQ 安装状态..."
    
    if command -v rabbitmq-server &> /dev/null; then
        log_success "RabbitMQ 已安装"
        return 0
    else
        log_error "RabbitMQ 未安装，请先安装 RabbitMQ"
        log_info "macOS: brew install rabbitmq"
        log_info "Ubuntu: sudo apt-get install rabbitmq-server"
        log_info "CentOS: sudo yum install rabbitmq-server"
        return 1
    fi
}

# 启动 RabbitMQ 服务
start_rabbitmq() {
    log_info "启动 RabbitMQ 服务..."
    
    # 检查服务是否已经运行
    if pgrep -f rabbitmq-server > /dev/null; then
        log_warning "RabbitMQ 服务已在运行"
        return 0
    fi
    
    # 尝试启动服务
    if command -v brew &> /dev/null; then
        # macOS with Homebrew
        brew services start rabbitmq
    elif command -v systemctl &> /dev/null; then
        # Linux with systemd
        sudo systemctl start rabbitmq-server
        sudo systemctl enable rabbitmq-server
    else
        # 直接启动
        sudo rabbitmq-server -detached
    fi
    
    # 等待服务启动
    log_info "等待 RabbitMQ 服务启动..."
    sleep 5
    
    # 验证服务状态
    if pgrep -f rabbitmq-server > /dev/null; then
        log_success "RabbitMQ 服务启动成功"
        return 0
    else
        log_error "RabbitMQ 服务启动失败"
        return 1
    fi
}

# 启用管理插件
enable_management_plugin() {
    log_info "启用 RabbitMQ 管理插件..."
    
    if rabbitmq-plugins enable rabbitmq_management; then
        log_success "管理插件启用成功"
        log_info "管理界面地址: http://localhost:15672"
        log_info "默认用户名/密码: guest/guest"
    else
        log_error "管理插件启用失败"
        return 1
    fi
}

# 创建用户和虚拟主机
setup_user_and_vhost() {
    log_info "设置用户和虚拟主机..."
    
    # 创建虚拟主机
    log_info "创建虚拟主机: chaiguanjia_vhost"
    if rabbitmqctl add_vhost chaiguanjia_vhost; then
        log_success "虚拟主机创建成功"
    else
        log_warning "虚拟主机可能已存在"
    fi
    
    # 创建用户
    log_info "创建用户: chaiguanjia"
    if rabbitmqctl add_user chaiguanjia chaiguanjia123; then
        log_success "用户创建成功"
    else
        log_warning "用户可能已存在"
    fi
    
    # 设置用户权限
    log_info "设置用户权限..."
    rabbitmqctl set_permissions -p chaiguanjia_vhost chaiguanjia ".*" ".*" ".*"
    
    # 设置用户标签
    log_info "设置用户标签..."
    rabbitmqctl set_user_tags chaiguanjia administrator
    
    log_success "用户和虚拟主机设置完成"
}

# 导入队列定义
import_definitions() {
    log_info "导入队列定义..."
    
    local definitions_file="../config/rabbitmq_definitions.json"
    
    if [ -f "$definitions_file" ]; then
        # 使用管理API导入定义
        curl -u chaiguanjia:chaiguanjia123 \
             -H "Content-Type: application/json" \
             -X POST \
             -d @"$definitions_file" \
             http://localhost:15672/api/definitions/chaiguanjia_vhost
        
        if [ $? -eq 0 ]; then
            log_success "队列定义导入成功"
        else
            log_error "队列定义导入失败"
            return 1
        fi
    else
        log_warning "队列定义文件不存在: $definitions_file"
        log_info "将手动创建基本队列结构..."
        
        # 手动创建基本结构
        create_basic_structure
    fi
}

# 创建基本队列结构
create_basic_structure() {
    log_info "创建基本队列结构..."
    
    # 使用 rabbitmqadmin 创建交换机和队列
    if command -v rabbitmqadmin &> /dev/null; then
        # 创建交换机
        rabbitmqadmin -u chaiguanjia -p chaiguanjia123 -V chaiguanjia_vhost declare exchange name=chaiguanjia.events type=topic durable=true
        rabbitmqadmin -u chaiguanjia -p chaiguanjia123 -V chaiguanjia_vhost declare exchange name=chaiguanjia.commands type=direct durable=true
        rabbitmqadmin -u chaiguanjia -p chaiguanjia123 -V chaiguanjia_vhost declare exchange name=chaiguanjia.dlx type=direct durable=true
        
        # 创建队列
        rabbitmqadmin -u chaiguanjia -p chaiguanjia123 -V chaiguanjia_vhost declare queue name=channel.events durable=true
        rabbitmqadmin -u chaiguanjia -p chaiguanjia123 -V chaiguanjia_vhost declare queue name=message.events durable=true
        rabbitmqadmin -u chaiguanjia -p chaiguanjia123 -V chaiguanjia_vhost declare queue name=ai.events durable=true
        rabbitmqadmin -u chaiguanjia -p chaiguanjia123 -V chaiguanjia_vhost declare queue name=user.events durable=true
        
        log_success "基本队列结构创建完成"
    else
        log_warning "rabbitmqadmin 未安装，跳过队列创建"
        log_info "请手动创建队列或安装 rabbitmqadmin"
    fi
}

# 验证设置
verify_setup() {
    log_info "验证 RabbitMQ 设置..."
    
    # 检查服务状态
    if ! pgrep -f rabbitmq-server > /dev/null; then
        log_error "RabbitMQ 服务未运行"
        return 1
    fi
    
    # 检查管理界面
    if curl -s http://localhost:15672 > /dev/null; then
        log_success "管理界面可访问"
    else
        log_warning "管理界面不可访问"
    fi
    
    # 检查用户连接
    if rabbitmqctl authenticate_user chaiguanjia chaiguanjia123 > /dev/null 2>&1; then
        log_success "用户认证成功"
    else
        log_error "用户认证失败"
        return 1
    fi
    
    log_success "RabbitMQ 设置验证完成"
}

# 主函数
main() {
    log_info "开始 RabbitMQ 设置..."
    
    # 检查安装
    if ! check_rabbitmq_installation; then
        exit 1
    fi
    
    # 启动服务
    if ! start_rabbitmq; then
        exit 1
    fi
    
    # 启用管理插件
    if ! enable_management_plugin; then
        exit 1
    fi
    
    # 等待管理插件启动
    log_info "等待管理插件启动..."
    sleep 10
    
    # 设置用户和虚拟主机
    if ! setup_user_and_vhost; then
        exit 1
    fi
    
    # 导入定义
    import_definitions
    
    # 验证设置
    if ! verify_setup; then
        exit 1
    fi
    
    log_success "RabbitMQ 设置完成！"
    log_info "连接信息:"
    log_info "  URL: amqp://chaiguanjia:chaiguanjia123@localhost:5672/chaiguanjia_vhost"
    log_info "  管理界面: http://localhost:15672"
    log_info "  用户名: chaiguanjia"
    log_info "  密码: chaiguanjia123"
}

# 运行主函数
main "$@"
