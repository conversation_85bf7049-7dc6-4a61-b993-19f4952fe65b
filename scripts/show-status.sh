#!/bin/bash

# 柴管家任务自动化 - 显示状态
# 功能：显示当前项目状态和任务分布

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的环境变量
check_environment() {
    local required_vars=("PROJECT_ID" "STATUS_FIELD_ID" "GH_TOKEN")
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "缺少必要的环境变量: $var"
            exit 1
        fi
    done
}

# 获取当前任务信息
get_current_task() {
    local current_task_file=".github/automation-state/current-task.json"
    
    if [ -f "$current_task_file" ]; then
        cat "$current_task_file"
    else
        echo "null"
    fi
}

# 获取项目统计信息
get_project_stats() {
    log_info "获取项目统计信息..."
    
    local query='
    query($projectId: ID!) {
      node(id: $projectId) {
        ... on ProjectV2 {
          title
          items(first: 200) {
            nodes {
              id
              content {
                ... on Issue {
                  id
                  number
                  title
                  url
                  state
                  createdAt
                  updatedAt
                }
              }
              fieldValues(first: 10) {
                nodes {
                  ... on ProjectV2ItemFieldSingleSelectValue {
                    field {
                      ... on ProjectV2SingleSelectField {
                        id
                        name
                      }
                    }
                    optionId
                    name
                  }
                }
              }
            }
          }
        }
      }
    }'
    
    gh api graphql -f query="$query" -f projectId="$PROJECT_ID"
}

# 分析项目数据
analyze_project_data() {
    local project_data="$1"
    
    # 统计各状态的任务数量
    local todo_count=$(echo "$project_data" | jq '[.data.node.items.nodes[] | select(.content != null and (.fieldValues.nodes[] | select(.field.name == "Status" and .name == "Todo")))] | length')
    local in_progress_count=$(echo "$project_data" | jq '[.data.node.items.nodes[] | select(.content != null and (.fieldValues.nodes[] | select(.field.name == "Status" and .name == "In Progress")))] | length')
    local done_count=$(echo "$project_data" | jq '[.data.node.items.nodes[] | select(.content != null and (.fieldValues.nodes[] | select(.field.name == "Status" and .name == "Done")))] | length')
    
    # 统计开放和关闭的Issues
    local open_issues=$(echo "$project_data" | jq '[.data.node.items.nodes[] | select(.content != null and .content.state == "OPEN")] | length')
    local closed_issues=$(echo "$project_data" | jq '[.data.node.items.nodes[] | select(.content != null and .content.state == "CLOSED")] | length')
    
    # 获取项目标题
    local project_title=$(echo "$project_data" | jq -r '.data.node.title')
    
    # 输出统计信息
    echo
    echo -e "${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║                    📊 项目状态概览                           ║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}"
    echo
    echo -e "${CYAN}项目名称:${NC} $project_title"
    echo -e "${CYAN}更新时间:${NC} $(date -u +"%Y-%m-%d %H:%M:%S UTC")"
    echo
    echo -e "${PURPLE}┌─ 任务状态分布 ─────────────────────────────────────────────┐${NC}"
    echo -e "${YELLOW}  📋 待办 (Todo):${NC}        $todo_count 个任务"
    echo -e "${BLUE}  🚀 进行中 (In Progress):${NC} $in_progress_count 个任务"
    echo -e "${GREEN}  ✅ 已完成 (Done):${NC}       $done_count 个任务"
    echo -e "${PURPLE}└────────────────────────────────────────────────────────────┘${NC}"
    echo
    echo -e "${PURPLE}┌─ Issue 状态分布 ───────────────────────────────────────────┐${NC}"
    echo -e "${GREEN}  🟢 开放状态:${NC}  $open_issues 个Issues"
    echo -e "${RED}  🔴 关闭状态:${NC}  $closed_issues 个Issues"
    echo -e "${CYAN}  📊 总计:${NC}      $((open_issues + closed_issues)) 个Issues"
    echo -e "${PURPLE}└────────────────────────────────────────────────────────────┘${NC}"
    
    # 返回统计数据
    jq -n \
        --arg project_title "$project_title" \
        --argjson todo_count "$todo_count" \
        --argjson in_progress_count "$in_progress_count" \
        --argjson done_count "$done_count" \
        --argjson open_issues "$open_issues" \
        --argjson closed_issues "$closed_issues" \
        '{
          projectTitle: $project_title,
          statusDistribution: {
            todo: $todo_count,
            inProgress: $in_progress_count,
            done: $done_count
          },
          issueDistribution: {
            open: $open_issues,
            closed: $closed_issues,
            total: ($open_issues + $closed_issues)
          }
        }'
}

# 显示当前任务信息
show_current_task() {
    local current_task=$(get_current_task)
    
    echo
    echo -e "${PURPLE}┌─ 当前任务状态 ─────────────────────────────────────────────┐${NC}"
    
    if [ "$current_task" = "null" ] || [ -z "$current_task" ]; then
        echo -e "${YELLOW}  ⏸️  当前没有正在进行的任务${NC}"
    else
        # 检查是否为有效的JSON
        if echo "$current_task" | jq . > /dev/null 2>&1; then
            local issue_number=$(echo "$current_task" | jq -r '.issueNumber')
            local title=$(echo "$current_task" | jq -r '.title')
            local url=$(echo "$current_task" | jq -r '.url')

            echo -e "${GREEN}  🚀 正在进行:${NC} Issue #$issue_number"
            echo -e "${CYAN}  📝 标题:${NC}     $title"
            echo -e "${CYAN}  🔗 链接:${NC}     $url"
        else
            echo -e "${RED}  ❌ 当前任务信息格式错误${NC}"
        fi
    fi
    
    echo -e "${PURPLE}└────────────────────────────────────────────────────────────┘${NC}"
}

# 显示待办任务列表
show_todo_tasks() {
    local project_data="$1"
    
    echo
    echo -e "${PURPLE}┌─ 待办任务列表 (按优先级排序) ──────────────────────────────┐${NC}"
    
    # 获取待办任务
    local todo_tasks=$(echo "$project_data" | jq -r '
    [.data.node.items.nodes[] | 
     select(.content != null and 
            .content.state == "OPEN" and
            (.fieldValues.nodes[] | select(.field.name == "Status" and .name == "Todo")))] |
    sort_by(.content.number) |
    .[:5] |  # 只显示前5个
    .[] | 
    "  📋 Issue #\(.content.number): \(.content.title)"')
    
    if [ -z "$todo_tasks" ]; then
        echo -e "${YELLOW}  🎉 没有待办任务！${NC}"
    else
        echo "$todo_tasks"
        
        # 检查是否还有更多任务
        local total_todo=$(echo "$project_data" | jq '[.data.node.items.nodes[] | select(.content != null and .content.state == "OPEN" and (.fieldValues.nodes[] | select(.field.name == "Status" and .name == "Todo")))] | length')
        if [ "$total_todo" -gt 5 ]; then
            echo -e "${CYAN}  ... 还有 $((total_todo - 5)) 个待办任务${NC}"
        fi
    fi
    
    echo -e "${PURPLE}└────────────────────────────────────────────────────────────┘${NC}"
}

# 显示最近操作日志
show_recent_operations() {
    local log_file=".github/automation-state/operations.log"
    
    echo
    echo -e "${PURPLE}┌─ 最近操作记录 ─────────────────────────────────────────────┐${NC}"
    
    if [ -f "$log_file" ]; then
        # 显示最近5条操作记录
        tail -n 5 "$log_file" | while IFS= read -r line; do
            if [ -n "$line" ]; then
                local timestamp=$(echo "$line" | jq -r '.timestamp')
                local operation=$(echo "$line" | jq -r '.operation')
                local status=$(echo "$line" | jq -r '.status')
                local issue_number=$(echo "$line" | jq -r '.task.issueNumber // "N/A"')
                
                local status_icon="❓"
                local status_color="$NC"
                
                case "$status" in
                    "success")
                        status_icon="✅"
                        status_color="$GREEN"
                        ;;
                    "failed")
                        status_icon="❌"
                        status_color="$RED"
                        ;;
                esac
                
                echo -e "  ${status_color}${status_icon} ${operation}${NC} Issue #${issue_number} - ${timestamp}"
            fi
        done
    else
        echo -e "${YELLOW}  📝 暂无操作记录${NC}"
    fi
    
    echo -e "${PURPLE}└────────────────────────────────────────────────────────────┘${NC}"
}

# 生成GitHub Actions摘要
generate_github_summary() {
    local stats="$1"
    
    if [ -n "$GITHUB_STEP_SUMMARY" ]; then
        local project_title=$(echo "$stats" | jq -r '.projectTitle')
        local todo_count=$(echo "$stats" | jq -r '.statusDistribution.todo')
        local in_progress_count=$(echo "$stats" | jq -r '.statusDistribution.inProgress')
        local done_count=$(echo "$stats" | jq -r '.statusDistribution.done')
        local total_issues=$(echo "$stats" | jq -r '.issueDistribution.total')
        
        local current_task=$(get_current_task)
        local current_task_info=""
        
        if [ "$current_task" != "null" ] && [ -n "$current_task" ] && echo "$current_task" | jq . > /dev/null 2>&1; then
            local issue_number=$(echo "$current_task" | jq -r '.issueNumber')
            local title=$(echo "$current_task" | jq -r '.title')
            local url=$(echo "$current_task" | jq -r '.url')
            current_task_info="**当前任务**: [Issue #$issue_number]($url) - $title"
        else
            current_task_info="**当前任务**: 无正在进行的任务"
        fi
        
        cat >> "$GITHUB_STEP_SUMMARY" << EOF
## 📊 $project_title - 状态概览

### 任务分布
- 📋 **待办**: $todo_count 个任务
- 🚀 **进行中**: $in_progress_count 个任务  
- ✅ **已完成**: $done_count 个任务
- 📊 **总计**: $total_issues 个Issues

### 当前状态
$current_task_info

**更新时间**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
EOF
    fi
}

# 主函数
main() {
    log_info "生成项目状态报告..."
    
    # 检查环境
    check_environment
    
    # 获取项目数据
    local project_data=$(get_project_stats)
    
    # 分析数据并显示统计信息
    local stats=$(analyze_project_data "$project_data")
    
    # 显示当前任务
    show_current_task
    
    # 显示待办任务列表
    show_todo_tasks "$project_data"
    
    # 显示最近操作记录
    show_recent_operations
    
    # 生成GitHub Actions摘要
    generate_github_summary "$stats"
    
    echo
    log_success "状态报告生成完成"
}

# 执行主函数
main "$@"
