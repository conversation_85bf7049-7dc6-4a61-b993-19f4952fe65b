#!/bin/bash

# 柴管家任务自动化 - 开始下一个任务
# 功能：从待办列表中选择优先级最高的任务并开始执行

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的环境变量
check_environment() {
    local required_vars=("PROJECT_ID" "STATUS_FIELD_ID" "TODO_OPTION_ID" "IN_PROGRESS_OPTION_ID" "GH_TOKEN")
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "缺少必要的环境变量: $var"
            exit 1
        fi
    done
}

# 获取当前正在进行的任务
get_current_task() {
    local current_task_file=".github/automation-state/current-task.json"

    if [ -f "$current_task_file" ]; then
        cat "$current_task_file"
    else
        echo "null"
    fi
}

# 保存当前任务信息
save_current_task() {
    local task_info="$1"
    local current_task_file=".github/automation-state/current-task.json"
    
    mkdir -p "$(dirname "$current_task_file")"
    echo "$task_info" > "$current_task_file"
}

# 获取待办任务列表
get_todo_tasks() {
    log_info "查询待办任务列表..."
    
    local query='
    query($projectId: ID!) {
      node(id: $projectId) {
        ... on ProjectV2 {
          items(first: 100, orderBy: {field: POSITION, direction: ASC}) {
            nodes {
              id
              content {
                ... on Issue {
                  id
                  number
                  title
                  url
                  state
                }
              }
              fieldValues(first: 10) {
                nodes {
                  ... on ProjectV2ItemFieldSingleSelectValue {
                    field {
                      ... on ProjectV2SingleSelectField {
                        id
                        name
                      }
                    }
                    optionId
                  }
                }
              }
            }
          }
        }
      }
    }'
    
    gh api graphql -f query="$query" -f projectId="$PROJECT_ID"
}

# 筛选待办状态的任务
filter_todo_tasks() {
    local all_tasks="$1"
    
    echo "$all_tasks" | jq --arg todo_id "$TODO_OPTION_ID" '
    .data.node.items.nodes[] | 
    select(
      .content != null and 
      .content.state == "OPEN" and
      (.fieldValues.nodes[] | select(.field.name == "Status" and .optionId == $todo_id))
    ) |
    {
      itemId: .id,
      issueId: .content.id,
      issueNumber: .content.number,
      title: .content.title,
      url: .content.url
    }'
}

# 选择下一个任务
select_next_task() {
    log_info "分析待办任务并选择下一个任务..."
    
    # 检查是否已有正在进行的任务
    local current_task=$(get_current_task)
    if [ "$current_task" != "null" ] && [ -n "$current_task" ]; then
        # 检查是否为有效的JSON
        if echo "$current_task" | jq . > /dev/null 2>&1; then
            local current_issue_number=$(echo "$current_task" | jq -r '.issueNumber // empty')
            if [ -n "$current_issue_number" ] && [ "$current_issue_number" != "null" ]; then
                log_warning "已有正在进行的任务: #$current_issue_number"
                log_warning "请先完成当前任务或使用 complete-task 操作"
                return 1
            fi
        fi
    fi
    
    # 获取所有项目
    local all_tasks=$(get_todo_tasks)
    
    # 筛选待办任务
    local todo_tasks=$(filter_todo_tasks "$all_tasks")
    
    if [ -z "$todo_tasks" ] || [ "$todo_tasks" = "" ]; then
        log_warning "没有找到待办任务"
        return 1
    fi
    
    # 选择第一个任务（最高优先级）
    local next_task=$(echo "$todo_tasks" | head -n 1)
    
    if [ -z "$next_task" ] || [ "$next_task" = "" ]; then
        log_warning "无法选择下一个任务"
        return 1
    fi
    
    echo "$next_task"
}

# 更新任务状态
update_task_status() {
    local item_id="$1"
    local new_status_id="$2"
    local task_title="$3"
    
    log_info "更新任务状态: $task_title"
    
    if [ "$DRY_RUN" = "true" ]; then
        log_warning "[预览模式] 将更新任务状态为进行中"
        return 0
    fi
    
    local mutation='
    mutation($projectId: ID!, $itemId: ID!, $fieldId: ID!, $optionId: String!) {
      updateProjectV2ItemFieldValue(input: {
        projectId: $projectId
        itemId: $itemId
        fieldId: $fieldId
        value: { singleSelectOptionId: $optionId }
      }) {
        projectV2Item {
          id
          content {
            ... on Issue {
              number
              title
            }
          }
        }
      }
    }'
    
    local result=$(gh api graphql \
        -f query="$mutation" \
        -f projectId="$PROJECT_ID" \
        -f itemId="$item_id" \
        -f fieldId="$STATUS_FIELD_ID" \
        -f optionId="$new_status_id")
    
    if echo "$result" | jq -e '.data.updateProjectV2ItemFieldValue' > /dev/null; then
        log_success "任务状态更新成功"
        return 0
    else
        log_error "任务状态更新失败"
        echo "$result" | jq '.errors // .'
        return 1
    fi
}

# 记录操作日志
log_operation() {
    local operation="$1"
    local task_info="$2"
    local status="$3"
    
    local log_file=".github/automation-state/operations.log"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    mkdir -p "$(dirname "$log_file")"
    
    local log_entry=$(jq -n \
        --arg timestamp "$timestamp" \
        --arg operation "$operation" \
        --arg status "$status" \
        --argjson task "$task_info" \
        '{
          timestamp: $timestamp,
          operation: $operation,
          status: $status,
          task: $task
        }')
    
    echo "$log_entry" >> "$log_file"
}

# 主函数
main() {
    log_info "开始执行任务选择和启动流程..."
    
    # 检查环境
    check_environment
    
    # 选择下一个任务
    local next_task=$(select_next_task)
    if [ $? -ne 0 ] || [ -z "$next_task" ]; then
        log_error "无法选择下一个任务"
        exit 1
    fi
    
    # 解析任务信息
    local item_id=$(echo "$next_task" | jq -r '.itemId')
    local issue_number=$(echo "$next_task" | jq -r '.issueNumber')
    local title=$(echo "$next_task" | jq -r '.title')
    local url=$(echo "$next_task" | jq -r '.url')
    
    log_info "选中的任务:"
    log_info "  Issue #$issue_number: $title"
    log_info "  URL: $url"
    
    # 更新任务状态为"进行中"
    if update_task_status "$item_id" "$IN_PROGRESS_OPTION_ID" "$title"; then
        # 保存当前任务信息
        if [ "$DRY_RUN" != "true" ]; then
            save_current_task "$next_task"
        fi
        
        # 记录操作日志
        log_operation "start-task" "$next_task" "success"
        
        log_success "任务已开始: #$issue_number - $title"
        log_info "任务URL: $url"
        
        # 输出GitHub Actions摘要
        if [ -n "$GITHUB_STEP_SUMMARY" ]; then
            cat >> "$GITHUB_STEP_SUMMARY" << EOF
## 🚀 任务已开始

**Issue #$issue_number**: $title

**状态**: 待办 → 进行中

**任务链接**: [$url]($url)

**开始时间**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
EOF
        fi
    else
        log_operation "start-task" "$next_task" "failed"
        log_error "任务启动失败"
        exit 1
    fi
}

# 执行主函数
main "$@"
