#!/bin/bash

# 开发环境启动脚本
# 启动所有必要的服务用于开发

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! docker compose version &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装，请先安装Node.js"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python3未安装，请先安装Python3"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 启动基础设施服务
start_infrastructure() {
    log_info "启动基础设施服务..."
    
    # 启动数据库、消息队列等服务
    docker compose -f docker-compose.dev.yml up -d postgres rabbitmq redis chromadb
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker compose -f docker-compose.dev.yml ps | grep -q "unhealthy\|exited"; then
        log_error "某些基础设施服务启动失败"
        docker compose -f docker-compose.dev.yml ps
        exit 1
    fi
    
    log_success "基础设施服务启动成功"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 等待PostgreSQL完全启动
    sleep 5
    
    # 运行数据库初始化脚本
    if [ -f "scripts/setup_database.sh" ]; then
        bash scripts/setup_database.sh
    else
        log_warning "数据库初始化脚本不存在，跳过"
    fi
    
    log_success "数据库初始化完成"
}

# 安装前端依赖
install_frontend_deps() {
    log_info "安装前端依赖..."
    
    cd frontend
    
    if [ ! -d "node_modules" ]; then
        log_info "安装npm依赖..."
        npm ci
    else
        log_info "前端依赖已存在，跳过安装"
    fi
    
    cd ..
    log_success "前端依赖安装完成"
}

# 安装后端依赖
install_backend_deps() {
    log_info "安装后端依赖..."
    
    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        log_info "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    log_info "安装Python依赖..."
    pip install -r requirements.txt
    
    log_success "后端依赖安装完成"
}

# 启动后端服务
start_backend() {
    log_info "启动后端服务..."
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 启动FastAPI应用
    cd src/Host
    uvicorn main:app --host 0.0.0.0 --port 8000 --reload &
    BACKEND_PID=$!
    cd ../..
    
    # 等待后端启动
    sleep 5
    
    # 检查后端是否启动成功
    if curl -f http://localhost:8000/health > /dev/null 2>&1; then
        log_success "后端服务启动成功 (PID: $BACKEND_PID)"
        echo $BACKEND_PID > .backend.pid
    else
        log_error "后端服务启动失败"
        exit 1
    fi
}

# 启动前端服务
start_frontend() {
    log_info "启动前端服务..."
    
    cd frontend
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    # 等待前端启动
    sleep 5
    
    # 检查前端是否启动成功
    if curl -f http://localhost:3000 > /dev/null 2>&1; then
        log_success "前端服务启动成功 (PID: $FRONTEND_PID)"
        echo $FRONTEND_PID > .frontend.pid
    else
        log_error "前端服务启动失败"
        exit 1
    fi
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    echo ""
    echo "🐘 PostgreSQL:     http://localhost:5432"
    echo "🐰 RabbitMQ:       http://localhost:15672 (admin/admin)"
    echo "📊 Redis:          http://localhost:6379"
    echo "🔍 ChromaDB:       http://localhost:8000"
    echo "🚀 后端API:        http://localhost:8000"
    echo "📱 前端应用:       http://localhost:3000"
    echo "📚 API文档:        http://localhost:8000/docs"
    echo ""
    log_success "开发环境启动完成！"
}

# 清理函数
cleanup() {
    log_info "正在停止服务..."
    
    # 停止前端
    if [ -f ".frontend.pid" ]; then
        kill $(cat .frontend.pid) 2>/dev/null || true
        rm .frontend.pid
    fi
    
    # 停止后端
    if [ -f ".backend.pid" ]; then
        kill $(cat .backend.pid) 2>/dev/null || true
        rm .backend.pid
    fi
    
    # 停止Docker服务
    docker compose -f docker-compose.dev.yml down
    
    log_success "服务已停止"
}

# 信号处理
trap cleanup EXIT INT TERM

# 主函数
main() {
    log_info "启动柴管家开发环境..."
    
    check_dependencies
    start_infrastructure
    init_database
    install_backend_deps
    install_frontend_deps
    start_backend
    start_frontend
    show_status
    
    # 保持脚本运行
    log_info "按 Ctrl+C 停止所有服务"
    while true; do
        sleep 1
    done
}

# 检查参数
case "${1:-start}" in
    "start")
        main
        ;;
    "stop")
        cleanup
        exit 0
        ;;
    "status")
        docker compose -f docker-compose.dev.yml ps
        ;;
    *)
        echo "用法: $0 [start|stop|status]"
        echo "  start  - 启动开发环境 (默认)"
        echo "  stop   - 停止开发环境"
        echo "  status - 查看服务状态"
        exit 1
        ;;
esac
