#!/bin/bash

# 柴管家任务自动化 - 状态同步
# 功能：同步GitHub事件到项目状态，处理自动化状态更新

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的环境变量
check_environment() {
    local required_vars=("PROJECT_ID" "STATUS_FIELD_ID" "DONE_OPTION_ID" "GH_TOKEN")
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            log_error "缺少必要的环境变量: $var"
            exit 1
        fi
    done
}

# 获取当前任务信息
get_current_task() {
    local current_task_file=".github/automation-state/current-task.json"
    
    if [ -f "$current_task_file" ]; then
        cat "$current_task_file"
    else
        echo "null"
    fi
}

# 检查项目中已关闭但状态未同步的Issues
check_closed_issues() {
    log_info "检查已关闭但状态未同步的Issues..."
    
    local query='
    query($projectId: ID!) {
      node(id: $projectId) {
        ... on ProjectV2 {
          items(first: 100) {
            nodes {
              id
              content {
                ... on Issue {
                  id
                  number
                  title
                  url
                  state
                }
              }
              fieldValues(first: 10) {
                nodes {
                  ... on ProjectV2ItemFieldSingleSelectValue {
                    field {
                      ... on ProjectV2SingleSelectField {
                        id
                        name
                      }
                    }
                    optionId
                  }
                }
              }
            }
          }
        }
      }
    }'
    
    local all_items=$(gh api graphql -f query="$query" -f projectId="$PROJECT_ID")
    
    # 筛选已关闭但项目状态不是"已完成"的Issues
    echo "$all_items" | jq --arg done_id "$DONE_OPTION_ID" '
    .data.node.items.nodes[] | 
    select(
      .content != null and 
      .content.state == "CLOSED" and
      (.fieldValues.nodes[] | select(.field.name == "Status" and .optionId != $done_id))
    ) |
    {
      itemId: .id,
      issueId: .content.id,
      issueNumber: .content.number,
      title: .content.title,
      url: .content.url,
      currentStatus: (.fieldValues.nodes[] | select(.field.name == "Status") | .optionId)
    }'
}

# 更新Issue状态为已完成
update_issue_to_done() {
    local item_id="$1"
    local issue_number="$2"
    local title="$3"
    
    log_info "同步Issue #$issue_number 状态为已完成: $title"
    
    if [ "$DRY_RUN" = "true" ]; then
        log_warning "[预览模式] 将同步Issue #$issue_number 状态为已完成"
        return 0
    fi
    
    local mutation='
    mutation($projectId: ID!, $itemId: ID!, $fieldId: ID!, $optionId: String!) {
      updateProjectV2ItemFieldValue(input: {
        projectId: $projectId
        itemId: $itemId
        fieldId: $fieldId
        value: { singleSelectOptionId: $optionId }
      }) {
        projectV2Item {
          id
          content {
            ... on Issue {
              number
              title
            }
          }
        }
      }
    }'
    
    local result=$(gh api graphql \
        -f query="$mutation" \
        -f projectId="$PROJECT_ID" \
        -f itemId="$item_id" \
        -f fieldId="$STATUS_FIELD_ID" \
        -f optionId="$DONE_OPTION_ID")
    
    if echo "$result" | jq -e '.data.updateProjectV2ItemFieldValue' > /dev/null; then
        log_success "Issue #$issue_number 状态同步成功"
        return 0
    else
        log_error "Issue #$issue_number 状态同步失败"
        echo "$result" | jq '.errors // .'
        return 1
    fi
}

# 检查当前任务是否已关闭
check_current_task_status() {
    local current_task=$(get_current_task)

    if [ "$current_task" = "null" ] || [ -z "$current_task" ]; then
        log_info "当前没有正在进行的任务"
        return 0
    fi

    # 检查是否为有效的JSON
    if ! echo "$current_task" | jq . > /dev/null 2>&1; then
        log_warning "当前任务信息格式错误，不是有效的JSON"
        return 1
    fi

    local issue_number=$(echo "$current_task" | jq -r '.issueNumber')
    local title=$(echo "$current_task" | jq -r '.title')
    
    log_info "检查当前任务状态: Issue #$issue_number"
    
    # 查询Issue当前状态
    local issue_state=$(gh api repos/$GITHUB_REPOSITORY/issues/$issue_number --jq '.state')
    
    if [ "$issue_state" = "closed" ]; then
        log_info "当前任务已关闭，将自动完成: Issue #$issue_number"
        
        # 调用完成任务脚本
        bash scripts/complete-task.sh "$issue_number"
        
        return 0
    else
        log_info "当前任务仍在进行中: Issue #$issue_number"
        return 0
    fi
}

# 记录同步操作
log_sync_operation() {
    local synced_issues="$1"
    local status="$2"
    
    local log_file=".github/automation-state/sync.log"
    local timestamp=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    mkdir -p "$(dirname "$log_file")"
    
    local log_entry=$(jq -n \
        --arg timestamp "$timestamp" \
        --arg status "$status" \
        --argjson issues "$synced_issues" \
        '{
          timestamp: $timestamp,
          operation: "sync-status",
          status: $status,
          syncedIssues: $issues
        }')
    
    echo "$log_entry" >> "$log_file"
}

# 主函数
main() {
    log_info "开始状态同步流程..."
    
    # 检查环境
    check_environment
    
    local sync_count=0
    local synced_issues="[]"
    
    # 检查当前任务状态
    check_current_task_status
    
    # 检查需要同步的已关闭Issues
    local closed_issues=$(check_closed_issues)
    
    if [ -n "$closed_issues" ] && [ "$closed_issues" != "" ]; then
        log_info "发现需要同步的已关闭Issues:"
        
        # 处理每个需要同步的Issue
        while IFS= read -r issue_info; do
            if [ -n "$issue_info" ] && [ "$issue_info" != "" ]; then
                local item_id=$(echo "$issue_info" | jq -r '.itemId')
                local issue_number=$(echo "$issue_info" | jq -r '.issueNumber')
                local title=$(echo "$issue_info" | jq -r '.title')
                
                log_info "  Issue #$issue_number: $title"
                
                if update_issue_to_done "$item_id" "$issue_number" "$title"; then
                    sync_count=$((sync_count + 1))
                    synced_issues=$(echo "$synced_issues" | jq ". + [$issue_info]")
                fi
            fi
        done <<< "$(echo "$closed_issues" | jq -c '.')"
        
    else
        log_info "没有发现需要同步的Issues"
    fi
    
    # 记录同步结果
    if [ $sync_count -gt 0 ]; then
        log_sync_operation "$synced_issues" "success"
        log_success "状态同步完成，共同步了 $sync_count 个Issues"
        
        # 输出GitHub Actions摘要
        if [ -n "$GITHUB_STEP_SUMMARY" ]; then
            cat >> "$GITHUB_STEP_SUMMARY" << EOF
## 🔄 状态同步完成

**同步数量**: $sync_count 个Issues

**同步时间**: $(date -u +"%Y-%m-%d %H:%M:%S UTC")

### 同步的Issues:
EOF
            echo "$synced_issues" | jq -r '.[] | "- Issue #\(.issueNumber): \(.title)"' >> "$GITHUB_STEP_SUMMARY"
        fi
        
        # 检查是否需要开始下一个任务
        local current_task=$(get_current_task)
        if [ "$current_task" = "null" ] || [ -z "$current_task" ] || ! echo "$current_task" | jq . > /dev/null 2>&1; then
            log_info "当前没有进行中的任务，尝试开始下一个任务..."
            if [ "$DRY_RUN" != "true" ]; then
                bash scripts/start-next-task.sh || log_info "没有可开始的新任务"
            fi
        fi
        
    else
        log_sync_operation "[]" "no-changes"
        log_info "状态同步完成，没有需要更新的Issues"
    fi
}

# 执行主函数
main "$@"
