#!/bin/bash

# 测试GitHub App连接和项目配置
set -e

echo "🔍 测试GitHub App连接..."

# 检查环境变量
if [ -z "$GH_TOKEN" ]; then
    echo "❌ 错误: GH_TOKEN 环境变量未设置"
    exit 1
fi

echo "✅ GitHub Token 已设置"

# 测试基本API连接
echo "🔗 测试GitHub API连接..."
user_info=$(gh api user)
echo "✅ 连接成功，用户: $(echo "$user_info" | jq -r '.login')"

# 个人项目模式
echo "👤 使用个人项目模式"

# 列出所有个人Projects V2
echo "📋 获取个人Projects V2列表..."

projects_query='
query {
  user(login: "Amoresdk") {
    projectsV2(first: 10) {
      nodes {
        id
        number
        title
        url
      }
    }
  }
}'

echo "👤 查询个人项目..."
projects_result=$(gh api graphql -f query="$projects_query")
echo "📊 个人Projects V2列表:"
echo "$projects_result" | jq '.data.user.projectsV2.nodes[] | {number: .number, title: .title, url: .url}'

# 检查特定个人项目
PROJECT_NUMBER=${PROJECT_NUMBER:-1}
echo "🎯 检查个人项目编号 $PROJECT_NUMBER..."

project_query='
query($user: String!, $number: Int!) {
  user(login: $user) {
    projectV2(number: $number) {
      id
      title
      fields(first: 20) {
        nodes {
          ... on ProjectV2Field {
            id
            name
          }
          ... on ProjectV2SingleSelectField {
            id
            name
            options {
              id
              name
            }
          }
        }
      }
    }
  }
}'

project_result=$(gh api graphql -f query="$project_query" -f user=Amoresdk -F number=$PROJECT_NUMBER)
project_path=".data.user.projectV2"

if echo "$project_result" | jq -e "$project_path" > /dev/null; then
    echo "✅ 项目找到:"
    echo "$project_result" | jq "$project_path | {id: .id, title: .title}"

    echo "📝 项目字段:"
    echo "$project_result" | jq "$project_path.fields.nodes[] | {name: .name, type: (if .options then \"SingleSelect\" else \"Field\" end)}"

    # 检查Status字段
    status_field=$(echo "$project_result" | jq "$project_path.fields.nodes[] | select(.name == \"Status\")")
    if [ "$status_field" != "null" ] && [ -n "$status_field" ]; then
        echo "✅ Status字段找到:"
        echo "$status_field" | jq '{id: .id, name: .name, options: .options}'
    else
        echo "❌ 未找到Status字段"
    fi
else
    echo "❌ 项目编号 $PROJECT_NUMBER 不存在"
    echo "错误信息:"
    echo "$project_result" | jq '.errors // empty'
fi

echo "🎉 测试完成"
