#!/usr/bin/env python3
"""
数据库连接测试脚本
测试PostgreSQL和ChromaDB连接是否正常
"""

import os
import sys
import asyncio
import asyncpg
import chromadb
from chromadb.config import Settings
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_postgresql():
    """测试PostgreSQL连接"""
    logger.info("测试 PostgreSQL 连接...")
    
    try:
        # 从环境变量获取数据库配置
        db_config = {
            'host': os.getenv('POSTGRES_HOST', 'localhost'),
            'port': int(os.getenv('POSTGRES_PORT', 5432)),
            'database': os.getenv('POSTGRES_DB', 'chaiguanjia'),
            'user': os.getenv('POSTGRES_USER', 'postgres'),
            'password': os.getenv('POSTGRES_PASSWORD', 'postgres')
        }
        
        # 连接数据库
        conn = await asyncpg.connect(**db_config)
        
        # 测试查询
        version = await conn.fetchval('SELECT version()')
        logger.info(f"✓ PostgreSQL 连接成功")
        logger.info(f"  版本: {version.split(',')[0]}")
        
        # 测试表是否存在
        tables = await conn.fetch("""
            SELECT schemaname, tablename 
            FROM pg_tables 
            WHERE schemaname IN ('user_management', 'channel_management', 'message_workbench', 'ai_assistant')
            ORDER BY schemaname, tablename
        """)
        
        if tables:
            logger.info(f"  找到 {len(tables)} 个业务表:")
            for table in tables:
                logger.info(f"    - {table['schemaname']}.{table['tablename']}")
        else:
            logger.warning("  未找到业务表，可能需要运行数据库初始化脚本")
        
        # 测试用户表
        user_count = await conn.fetchval('SELECT COUNT(*) FROM user_management.users')
        logger.info(f"  用户数量: {user_count}")
        
        await conn.close()
        return True
        
    except Exception as e:
        logger.error(f"✗ PostgreSQL 连接失败: {e}")
        return False

def test_chromadb():
    """测试ChromaDB连接"""
    logger.info("测试 ChromaDB 连接...")
    
    try:
        # 获取数据库路径
        db_path = os.getenv('CHROMA_DB_PATH', './data/chromadb')
        
        # 创建客户端
        client = chromadb.PersistentClient(
            path=db_path,
            settings=Settings(
                anonymized_telemetry=False,
                allow_reset=False
            )
        )
        
        # 获取所有集合
        collections = client.list_collections()
        logger.info(f"✓ ChromaDB 连接成功")
        logger.info(f"  数据路径: {db_path}")
        logger.info(f"  集合数量: {len(collections)}")
        
        for collection in collections:
            count = collection.count()
            logger.info(f"    - {collection.name}: {count} 个文档")
        
        # 测试知识库集合
        try:
            knowledge_collection = client.get_collection("knowledge_base")
            sample_results = knowledge_collection.query(
                query_texts=["课程价格"],
                n_results=1
            )
            if sample_results['documents']:
                logger.info("  ✓ 知识库查询测试成功")
            else:
                logger.warning("  知识库为空，建议添加一些数据")
        except Exception as e:
            logger.warning(f"  知识库查询测试失败: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ ChromaDB 连接失败: {e}")
        return False

async def test_database_integration():
    """测试数据库集成"""
    logger.info("测试数据库集成...")
    
    try:
        # 测试PostgreSQL
        pg_success = await test_postgresql()
        
        # 测试ChromaDB
        chroma_success = test_chromadb()
        
        if pg_success and chroma_success:
            logger.info("✓ 所有数据库连接正常")
            return True
        else:
            logger.error("✗ 部分数据库连接失败")
            return False
            
    except Exception as e:
        logger.error(f"数据库集成测试失败: {e}")
        return False

def check_environment():
    """检查环境变量"""
    logger.info("检查环境变量配置...")
    
    required_vars = [
        'POSTGRES_HOST',
        'POSTGRES_PORT', 
        'POSTGRES_DB',
        'POSTGRES_USER',
        'POSTGRES_PASSWORD',
        'CHROMA_DB_PATH'
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            # 隐藏密码
            display_value = "***" if "PASSWORD" in var else value
            logger.info(f"  {var}: {display_value}")
        else:
            missing_vars.append(var)
    
    if missing_vars:
        logger.warning(f"缺少环境变量: {', '.join(missing_vars)}")
        logger.info("将使用默认值")
    else:
        logger.info("✓ 所有环境变量已配置")

async def main():
    """主函数"""
    logger.info("开始数据库连接测试...")
    
    # 检查环境变量
    check_environment()
    
    # 测试数据库连接
    success = await test_database_integration()
    
    if success:
        logger.info("✓ 数据库测试完成，所有连接正常")
        sys.exit(0)
    else:
        logger.error("✗ 数据库测试失败")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
