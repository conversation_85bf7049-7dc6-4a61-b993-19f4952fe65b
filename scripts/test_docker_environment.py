#!/usr/bin/env python3
"""
Docker环境测试脚本
测试Docker容器化环境的各项功能
"""

import asyncio
import os
import sys
import time
import requests
import logging
from typing import Dict, List, Tuple

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DockerEnvironmentTester:
    """Docker环境测试器"""
    
    def __init__(self):
        self.services = {
            'frontend': {
                'url': 'http://localhost:3001',
                'health_endpoint': '/health',
                'name': '前端服务'
            },
            'backend': {
                'url': 'http://localhost:8001',
                'health_endpoint': '/health',
                'name': '后端服务'
            },
            'backend_docs': {
                'url': 'http://localhost:8001',
                'health_endpoint': '/docs',
                'name': '后端API文档'
            },
            'rabbitmq_management': {
                'url': 'http://localhost:15673',
                'health_endpoint': '/',
                'name': 'RabbitMQ管理界面'
            },
            'chromadb': {
                'url': 'http://localhost:8002',
                'health_endpoint': '/api/v1/heartbeat',
                'name': 'ChromaDB'
            }
        }
        
        self.database_services = {
            'postgres': {
                'host': 'localhost',
                'port': 5433,
                'name': 'PostgreSQL数据库'
            },
            'redis': {
                'host': 'localhost',
                'port': 6380,
                'name': 'Redis缓存'
            }
        }
    
    def test_service_health(self, service_name: str, service_config: Dict) -> Tuple[bool, str]:
        """测试服务健康状态"""
        try:
            url = f"{service_config['url']}{service_config['health_endpoint']}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                return True, f"✓ {service_config['name']} 健康"
            else:
                return False, f"✗ {service_config['name']} 返回状态码: {response.status_code}"
                
        except requests.exceptions.ConnectionError:
            return False, f"✗ {service_config['name']} 连接失败"
        except requests.exceptions.Timeout:
            return False, f"✗ {service_config['name']} 连接超时"
        except Exception as e:
            return False, f"✗ {service_config['name']} 测试失败: {str(e)}"
    
    def test_database_connection(self, db_name: str, db_config: Dict) -> Tuple[bool, str]:
        """测试数据库连接"""
        import socket
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex((db_config['host'], db_config['port']))
            sock.close()
            
            if result == 0:
                return True, f"✓ {db_config['name']} 连接正常"
            else:
                return False, f"✗ {db_config['name']} 连接失败"
                
        except Exception as e:
            return False, f"✗ {db_config['name']} 测试失败: {str(e)}"
    
    def test_api_endpoints(self) -> List[Tuple[bool, str]]:
        """测试API端点"""
        results = []
        
        # 测试基础API端点
        api_endpoints = [
            ('/health', 'GET', '健康检查'),
            ('/api/v1/users/me', 'GET', '用户信息'),
            ('/api/v1/channels', 'GET', '渠道列表'),
        ]
        
        base_url = 'http://localhost:8001'
        
        for endpoint, method, description in api_endpoints:
            try:
                if method == 'GET':
                    response = requests.get(f"{base_url}{endpoint}", timeout=10)
                else:
                    response = requests.request(method, f"{base_url}{endpoint}", timeout=10)
                
                # 对于需要认证的端点，401是预期的
                if response.status_code in [200, 401, 404]:
                    results.append((True, f"✓ API {description} ({endpoint}) 响应正常"))
                else:
                    results.append((False, f"✗ API {description} ({endpoint}) 状态码: {response.status_code}"))
                    
            except Exception as e:
                results.append((False, f"✗ API {description} ({endpoint}) 测试失败: {str(e)}"))
        
        return results
    
    def test_frontend_routing(self) -> List[Tuple[bool, str]]:
        """测试前端路由"""
        results = []
        
        # 测试前端路由
        frontend_routes = [
            ('/', '首页'),
            ('/dashboard', '仪表板'),
            ('/channels', '渠道管理'),
            ('/messages', '消息工作台'),
            ('/ai-assistant', 'AI助手'),
            ('/settings', '设置'),
        ]
        
        base_url = 'http://localhost:3001'
        
        for route, description in frontend_routes:
            try:
                response = requests.get(f"{base_url}{route}", timeout=10)
                
                if response.status_code == 200:
                    results.append((True, f"✓ 前端 {description} ({route}) 可访问"))
                else:
                    results.append((False, f"✗ 前端 {description} ({route}) 状态码: {response.status_code}"))
                    
            except Exception as e:
                results.append((False, f"✗ 前端 {description} ({route}) 测试失败: {str(e)}"))
        
        return results
    
    def test_container_logs(self) -> List[Tuple[bool, str]]:
        """检查容器日志是否有错误"""
        results = []
        
        try:
            import subprocess
            
            # 检查各个容器的日志
            containers = [
                'chaiguanjia-backend-dev',
                'chaiguanjia-frontend-dev',
                'chaiguanjia-postgres-dev',
                'chaiguanjia-rabbitmq-dev',
                'chaiguanjia-redis-dev',
                'chaiguanjia-chromadb-dev'
            ]
            
            for container in containers:
                try:
                    # 获取最近的日志
                    result = subprocess.run(
                        ['docker', 'logs', '--tail', '50', container],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )
                    
                    if result.returncode == 0:
                        # 检查是否有错误关键词
                        error_keywords = ['ERROR', 'FATAL', 'CRITICAL', 'Exception', 'Traceback']
                        logs = result.stdout + result.stderr
                        
                        has_errors = any(keyword in logs for keyword in error_keywords)
                        
                        if has_errors:
                            results.append((False, f"⚠ {container} 日志中发现错误"))
                        else:
                            results.append((True, f"✓ {container} 日志正常"))
                    else:
                        results.append((False, f"✗ 无法获取 {container} 日志"))
                        
                except subprocess.TimeoutExpired:
                    results.append((False, f"✗ 获取 {container} 日志超时"))
                except Exception as e:
                    results.append((False, f"✗ 检查 {container} 日志失败: {str(e)}"))
        
        except ImportError:
            results.append((False, "✗ 无法导入subprocess模块"))
        
        return results
    
    def run_comprehensive_test(self) -> Dict[str, List[Tuple[bool, str]]]:
        """运行综合测试"""
        logger.info("开始Docker环境综合测试...")
        
        test_results = {}
        
        # 等待服务启动
        logger.info("等待服务启动...")
        time.sleep(10)
        
        # 测试HTTP服务
        logger.info("测试HTTP服务...")
        http_results = []
        for service_name, service_config in self.services.items():
            result = self.test_service_health(service_name, service_config)
            http_results.append(result)
        test_results['HTTP服务'] = http_results
        
        # 测试数据库连接
        logger.info("测试数据库连接...")
        db_results = []
        for db_name, db_config in self.database_services.items():
            result = self.test_database_connection(db_name, db_config)
            db_results.append(result)
        test_results['数据库连接'] = db_results
        
        # 测试API端点
        logger.info("测试API端点...")
        api_results = self.test_api_endpoints()
        test_results['API端点'] = api_results
        
        # 测试前端路由
        logger.info("测试前端路由...")
        frontend_results = self.test_frontend_routing()
        test_results['前端路由'] = frontend_results
        
        # 检查容器日志
        logger.info("检查容器日志...")
        log_results = self.test_container_logs()
        test_results['容器日志'] = log_results
        
        return test_results
    
    def print_test_results(self, test_results: Dict[str, List[Tuple[bool, str]]]):
        """打印测试结果"""
        total_tests = 0
        passed_tests = 0
        
        print("\n" + "="*60)
        print("Docker环境测试结果")
        print("="*60)
        
        for category, results in test_results.items():
            print(f"\n{category}:")
            print("-" * 40)
            
            for success, message in results:
                print(f"  {message}")
                total_tests += 1
                if success:
                    passed_tests += 1
        
        print("\n" + "="*60)
        print(f"测试总结: {passed_tests}/{total_tests} 通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！Docker环境运行正常")
            return True
        else:
            print("❌ 部分测试失败，请检查相关服务")
            return False


def main():
    """主函数"""
    tester = DockerEnvironmentTester()
    
    try:
        # 运行测试
        results = tester.run_comprehensive_test()
        
        # 打印结果
        success = tester.print_test_results(results)
        
        # 返回适当的退出码
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        sys.exit(1)
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
