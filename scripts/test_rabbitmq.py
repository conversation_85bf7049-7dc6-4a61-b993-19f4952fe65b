#!/usr/bin/env python3
"""
RabbitMQ连接测试脚本
测试RabbitMQ连接和基本功能
"""

import asyncio
import os
import sys
import uuid
from datetime import datetime
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from chaiguanjia.core.message_queue import MessageQueueManager, MessageEvent, MessageCommand

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_rabbitmq_connection():
    """测试RabbitMQ连接"""
    logger.info("测试 RabbitMQ 连接...")
    
    try:
        # 创建消息队列管理器
        mq_manager = MessageQueueManager()
        
        # 连接到RabbitMQ
        await mq_manager.connect()
        
        # 健康检查
        health = await mq_manager.health_check()
        logger.info(f"健康检查结果: {health}")
        
        if health['status'] == 'healthy':
            logger.info("✓ RabbitMQ 连接测试成功")
            return mq_manager
        else:
            logger.error("✗ RabbitMQ 健康检查失败")
            return None
            
    except Exception as e:
        logger.error(f"✗ RabbitMQ 连接测试失败: {e}")
        return None


async def test_message_publishing(mq_manager: MessageQueueManager):
    """测试消息发布"""
    logger.info("测试消息发布...")
    
    try:
        # 创建测试事件
        test_event = MessageEvent(
            event_type="test.event",
            event_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            source="test_script",
            data={
                "message": "这是一个测试事件",
                "test_id": 12345
            }
        )
        
        # 发布事件
        await mq_manager.publish_event(test_event, "channel.test")
        logger.info("✓ 事件发布成功")
        
        # 创建测试命令
        test_command = MessageCommand(
            command_type="test.command",
            command_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            source="test_script",
            data={
                "action": "test_action",
                "parameters": {"param1": "value1"}
            }
        )
        
        # 发布命令
        await mq_manager.publish_command(test_command, "channel.commands")
        logger.info("✓ 命令发布成功")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 消息发布测试失败: {e}")
        return False


async def test_message_consumption(mq_manager: MessageQueueManager):
    """测试消息消费"""
    logger.info("测试消息消费...")
    
    try:
        # 消息处理计数器
        message_count = 0
        
        async def test_message_handler(message_data):
            nonlocal message_count
            message_count += 1
            logger.info(f"收到消息 #{message_count}: {message_data}")
        
        # 订阅队列
        await mq_manager.subscribe_to_queue("channel.events", test_message_handler)
        logger.info("✓ 队列订阅成功")
        
        # 等待一段时间以接收消息
        await asyncio.sleep(2)
        
        if message_count > 0:
            logger.info(f"✓ 消息消费测试成功，处理了 {message_count} 条消息")
        else:
            logger.warning("⚠ 未收到任何消息，可能队列为空")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ 消息消费测试失败: {e}")
        return False


async def test_queue_management(mq_manager: MessageQueueManager):
    """测试队列管理功能"""
    logger.info("测试队列管理功能...")
    
    try:
        # 检查队列状态
        logger.info(f"已声明的交换机: {list(mq_manager.exchanges.keys())}")
        logger.info(f"已声明的队列: {list(mq_manager.queues.keys())}")
        logger.info(f"活跃的消费者: {list(mq_manager.consumers.keys())}")
        
        # 验证必要的交换机和队列是否存在
        required_exchanges = ['events', 'commands', 'dlx']
        required_queues = ['channel.events', 'message.events', 'ai.events', 'user.events']
        
        missing_exchanges = [ex for ex in required_exchanges if ex not in mq_manager.exchanges]
        missing_queues = [q for q in required_queues if q not in mq_manager.queues]
        
        if missing_exchanges:
            logger.error(f"✗ 缺少交换机: {missing_exchanges}")
            return False
        
        if missing_queues:
            logger.error(f"✗ 缺少队列: {missing_queues}")
            return False
        
        logger.info("✓ 队列管理功能测试成功")
        return True
        
    except Exception as e:
        logger.error(f"✗ 队列管理功能测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    logger.info("开始 RabbitMQ 功能测试...")
    
    # 测试连接
    mq_manager = await test_rabbitmq_connection()
    if not mq_manager:
        logger.error("连接测试失败，退出")
        sys.exit(1)
    
    try:
        # 测试队列管理
        queue_test = await test_queue_management(mq_manager)
        
        # 测试消息发布
        publish_test = await test_message_publishing(mq_manager)
        
        # 测试消息消费
        consume_test = await test_message_consumption(mq_manager)
        
        # 汇总测试结果
        all_tests_passed = queue_test and publish_test and consume_test
        
        if all_tests_passed:
            logger.info("✓ 所有 RabbitMQ 测试通过")
            exit_code = 0
        else:
            logger.error("✗ 部分 RabbitMQ 测试失败")
            exit_code = 1
        
    finally:
        # 断开连接
        await mq_manager.disconnect()
        logger.info("RabbitMQ 连接已关闭")
    
    sys.exit(exit_code)


if __name__ == "__main__":
    # 设置环境变量（如果未设置）
    if not os.getenv('RABBITMQ_URL'):
        os.environ['RABBITMQ_URL'] = 'amqp://chaiguanjia:chaiguanjia123@localhost:5672/chaiguanjia_vhost'
    
    asyncio.run(main())
