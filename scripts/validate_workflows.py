#!/usr/bin/env python3
"""
GitHub Actions 工作流验证脚本
验证工作流文件的语法和配置
"""

import os
import sys
import yaml
import json
from pathlib import Path
from typing import Dict, List, Any

import structlog
from rich.console import Console
from rich.panel import Panel
from rich.table import Table

console = Console()
logger = structlog.get_logger()


class WorkflowValidator:
    """工作流验证器"""
    
    def __init__(self):
        self.workflows_dir = Path(".github/workflows")
        self.errors: List[str] = []
        self.warnings: List[str] = []
        
    def validate_all(self) -> bool:
        """验证所有工作流文件"""
        console.print(Panel.fit("🔍 验证GitHub Actions工作流", style="bold blue"))
        
        if not self.workflows_dir.exists():
            self.errors.append("工作流目录 .github/workflows 不存在")
            return False
        
        workflow_files = list(self.workflows_dir.glob("*.yml")) + list(self.workflows_dir.glob("*.yaml"))
        
        if not workflow_files:
            self.errors.append("没有找到工作流文件")
            return False
        
        console.print(f"找到 {len(workflow_files)} 个工作流文件")
        
        all_valid = True
        for workflow_file in workflow_files:
            console.print(f"\n验证: {workflow_file.name}")
            if not self.validate_workflow(workflow_file):
                all_valid = False
        
        self.display_results()
        return all_valid
    
    def validate_workflow(self, workflow_file: Path) -> bool:
        """验证单个工作流文件"""
        try:
            # 检查YAML语法
            with open(workflow_file, 'r', encoding='utf-8') as f:
                workflow_data = yaml.safe_load(f)
            
            if not isinstance(workflow_data, dict):
                self.errors.append(f"{workflow_file.name}: 工作流文件格式错误")
                return False
            
            # 验证必需字段
            required_fields = ['name', 'on', 'jobs']
            for field in required_fields:
                if field not in workflow_data:
                    self.errors.append(f"{workflow_file.name}: 缺少必需字段 '{field}'")
                    return False
            
            # 验证触发条件
            self.validate_triggers(workflow_file.name, workflow_data.get('on', {}))
            
            # 验证作业
            self.validate_jobs(workflow_file.name, workflow_data.get('jobs', {}))
            
            console.print(f"  ✅ {workflow_file.name} 验证通过")
            return True
            
        except yaml.YAMLError as e:
            self.errors.append(f"{workflow_file.name}: YAML语法错误 - {str(e)}")
            return False
        except Exception as e:
            self.errors.append(f"{workflow_file.name}: 验证失败 - {str(e)}")
            return False
    
    def validate_triggers(self, filename: str, triggers: Dict[str, Any]) -> None:
        """验证触发条件"""
        if not triggers:
            self.warnings.append(f"{filename}: 没有配置触发条件")
            return
        
        # 检查常见的触发条件
        common_triggers = ['push', 'pull_request', 'workflow_dispatch', 'schedule']
        has_common_trigger = any(trigger in triggers for trigger in common_triggers)
        
        if not has_common_trigger:
            self.warnings.append(f"{filename}: 没有配置常见的触发条件")
        
        # 验证push触发条件
        if 'push' in triggers:
            push_config = triggers['push']
            if isinstance(push_config, dict) and 'branches' in push_config:
                branches = push_config['branches']
                if not isinstance(branches, list) or not branches:
                    self.warnings.append(f"{filename}: push触发条件的分支配置可能有问题")
    
    def validate_jobs(self, filename: str, jobs: Dict[str, Any]) -> None:
        """验证作业配置"""
        if not jobs:
            self.errors.append(f"{filename}: 没有定义任何作业")
            return
        
        for job_name, job_config in jobs.items():
            if not isinstance(job_config, dict):
                self.errors.append(f"{filename}: 作业 '{job_name}' 配置格式错误")
                continue
            
            # 检查必需字段
            if 'runs-on' not in job_config:
                self.errors.append(f"{filename}: 作业 '{job_name}' 缺少 'runs-on' 字段")
            
            # 检查步骤
            if 'steps' not in job_config:
                self.warnings.append(f"{filename}: 作业 '{job_name}' 没有定义步骤")
            else:
                steps = job_config['steps']
                if not isinstance(steps, list) or not steps:
                    self.warnings.append(f"{filename}: 作业 '{job_name}' 的步骤配置可能有问题")
    
    def display_results(self) -> None:
        """显示验证结果"""
        # 创建结果表格
        table = Table(title="工作流验证结果")
        table.add_column("类型", style="cyan", no_wrap=True)
        table.add_column("数量", style="magenta")
        table.add_column("状态", style="green")
        
        error_count = len(self.errors)
        warning_count = len(self.warnings)
        
        table.add_row("错误", str(error_count), "❌" if error_count > 0 else "✅")
        table.add_row("警告", str(warning_count), "⚠️" if warning_count > 0 else "✅")
        
        console.print(table)
        
        # 显示错误信息
        if self.errors:
            console.print("\n[bold red]错误:[/bold red]")
            for error in self.errors:
                console.print(f"  • {error}")
        
        # 显示警告信息
        if self.warnings:
            console.print("\n[bold yellow]警告:[/bold yellow]")
            for warning in self.warnings:
                console.print(f"  • {warning}")
        
        # 显示总结
        if not self.errors and not self.warnings:
            console.print(Panel.fit(
                "🎉 所有工作流文件验证通过！",
                style="bold green"
            ))
        elif not self.errors:
            console.print(Panel.fit(
                f"✅ 工作流文件验证通过，但有 {warning_count} 个警告需要注意。",
                style="bold yellow"
            ))
        else:
            console.print(Panel.fit(
                f"❌ 工作流文件验证失败，发现 {error_count} 个错误。",
                style="bold red"
            ))


def check_github_actions_status():
    """检查GitHub Actions相关状态"""
    console.print("\n[bold blue]GitHub Actions 环境检查:[/bold blue]")
    
    # 检查Git仓库
    if not Path(".git").exists():
        console.print("❌ 当前目录不是Git仓库")
        return False
    
    # 检查远程仓库
    try:
        import subprocess
        result = subprocess.run(['git', 'remote', 'get-url', 'origin'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            remote_url = result.stdout.strip()
            console.print(f"✅ 远程仓库: {remote_url}")
            
            # 检查是否是GitHub仓库
            if 'github.com' in remote_url:
                console.print("✅ 这是一个GitHub仓库")
                
                # 提取仓库信息
                if remote_url.startswith('**************:'):
                    repo_path = remote_url.replace('**************:', '').replace('.git', '')
                elif remote_url.startswith('https://github.com/'):
                    repo_path = remote_url.replace('https://github.com/', '').replace('.git', '')
                else:
                    repo_path = "unknown"
                
                actions_url = f"https://github.com/{repo_path}/actions"
                console.print(f"🔗 Actions页面: {actions_url}")
            else:
                console.print("⚠️  这不是GitHub仓库，GitHub Actions不会工作")
        else:
            console.print("❌ 没有配置远程仓库")
            return False
    except Exception as e:
        console.print(f"❌ 检查远程仓库失败: {str(e)}")
        return False
    
    # 检查工作流文件是否已提交
    try:
        result = subprocess.run(['git', 'ls-files', '.github/workflows/'], 
                              capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            committed_files = result.stdout.strip().split('\n')
            console.print(f"✅ 已提交的工作流文件: {len(committed_files)} 个")
            for file in committed_files:
                console.print(f"  • {file}")
        else:
            console.print("❌ 工作流文件尚未提交到Git仓库")
            console.print("💡 请运行: git add .github/ && git commit -m 'Add GitHub Actions workflows'")
            return False
    except Exception as e:
        console.print(f"❌ 检查提交状态失败: {str(e)}")
        return False
    
    return True


def main():
    """主函数"""
    validator = WorkflowValidator()
    
    # 验证工作流文件
    workflow_valid = validator.validate_all()
    
    # 检查GitHub Actions状态
    github_status = check_github_actions_status()
    
    if workflow_valid and github_status:
        console.print("\n[bold green]✅ GitHub Actions配置验证成功！[/bold green]")
        sys.exit(0)
    else:
        console.print("\n[bold red]❌ GitHub Actions配置验证失败！[/bold red]")
        sys.exit(1)


if __name__ == "__main__":
    main()
