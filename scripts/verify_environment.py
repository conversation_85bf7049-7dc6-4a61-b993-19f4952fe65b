#!/usr/bin/env python3
"""
环境验证脚本
验证开发环境是否正确配置和运行
"""

import asyncio
import json
import os
import subprocess
import sys
import time
from pathlib import Path
from typing import Dict, List, Tuple

import aiohttp
import asyncpg
import structlog
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.table import Table

# 设置控制台
console = Console()
logger = structlog.get_logger()


class EnvironmentVerifier:
    """环境验证器"""
    
    def __init__(self):
        self.results: Dict[str, bool] = {}
        self.errors: List[str] = []
        
    async def verify_all(self) -> bool:
        """验证所有环境组件"""
        console.print(Panel.fit("🔍 开始环境验证", style="bold blue"))
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console,
        ) as progress:
            
            # 验证任务列表
            tasks = [
                ("检查Python环境", self.verify_python),
                ("检查Node.js环境", self.verify_nodejs),
                ("检查Docker环境", self.verify_docker),
                ("验证数据库连接", self.verify_database),
                ("验证消息队列", self.verify_rabbitmq),
                ("验证向量数据库", self.verify_chromadb),
                ("验证后端API", self.verify_backend_api),
                ("验证前端应用", self.verify_frontend),
                ("验证CI/CD配置", self.verify_cicd),
            ]
            
            for description, verify_func in tasks:
                task = progress.add_task(description, total=None)
                try:
                    result = await verify_func()
                    self.results[description] = result
                    if result:
                        progress.update(task, description=f"✅ {description}")
                    else:
                        progress.update(task, description=f"❌ {description}")
                except Exception as e:
                    self.results[description] = False
                    self.errors.append(f"{description}: {str(e)}")
                    progress.update(task, description=f"❌ {description}")
                
                progress.remove_task(task)
        
        # 显示结果
        self.display_results()
        
        # 返回总体结果
        return all(self.results.values())
    
    async def verify_python(self) -> bool:
        """验证Python环境"""
        try:
            # 检查Python版本
            version = sys.version_info
            if version.major != 3 or version.minor < 8:
                self.errors.append(f"Python版本过低: {version.major}.{version.minor}, 需要3.8+")
                return False
            
            # 检查必要的包
            required_packages = [
                'fastapi', 'uvicorn', 'sqlalchemy', 'asyncpg',
                'aio_pika', 'chromadb', 'structlog', 'pydantic'
            ]
            
            for package in required_packages:
                try:
                    __import__(package)
                except ImportError:
                    self.errors.append(f"缺少Python包: {package}")
                    return False
            
            return True
            
        except Exception as e:
            self.errors.append(f"Python环境检查失败: {str(e)}")
            return False
    
    async def verify_nodejs(self) -> bool:
        """验证Node.js环境"""
        try:
            # 检查Node.js版本
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode != 0:
                self.errors.append("Node.js未安装")
                return False
            
            version = result.stdout.strip()
            major_version = int(version.lstrip('v').split('.')[0])
            if major_version < 18:
                self.errors.append(f"Node.js版本过低: {version}, 需要18+")
                return False
            
            # 检查npm
            result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
            if result.returncode != 0:
                self.errors.append("npm未安装")
                return False
            
            # 检查前端依赖
            frontend_dir = Path("frontend")
            if not (frontend_dir / "node_modules").exists():
                self.errors.append("前端依赖未安装，请运行: cd frontend && npm install")
                return False
            
            return True
            
        except Exception as e:
            self.errors.append(f"Node.js环境检查失败: {str(e)}")
            return False
    
    async def verify_docker(self) -> bool:
        """验证Docker环境"""
        try:
            # 检查Docker
            result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
            if result.returncode != 0:
                self.errors.append("Docker未安装")
                return False
            
            # 检查Docker Compose
            result = subprocess.run(['docker', 'compose', 'version'], capture_output=True, text=True)
            if result.returncode != 0:
                self.errors.append("Docker Compose未安装")
                return False
            
            # 检查Docker是否运行
            result = subprocess.run(['docker', 'info'], capture_output=True, text=True)
            if result.returncode != 0:
                self.errors.append("Docker服务未运行")
                return False
            
            return True
            
        except Exception as e:
            self.errors.append(f"Docker环境检查失败: {str(e)}")
            return False
    
    async def verify_database(self) -> bool:
        """验证数据库连接"""
        try:
            # 尝试连接PostgreSQL
            conn = await asyncpg.connect(
                host="localhost",
                port=5432,
                user="chaiguanjia",
                password="chaiguanjia123",
                database="chaiguanjia"
            )
            
            # 执行简单查询
            result = await conn.fetchval("SELECT 1")
            await conn.close()
            
            if result != 1:
                self.errors.append("数据库查询失败")
                return False
            
            return True
            
        except Exception as e:
            self.errors.append(f"数据库连接失败: {str(e)}")
            return False
    
    async def verify_rabbitmq(self) -> bool:
        """验证RabbitMQ连接"""
        try:
            import aio_pika
            
            # 尝试连接RabbitMQ
            connection = await aio_pika.connect_robust(
                "amqp://chaiguanjia:chaiguanjia123@localhost:5672/"
            )
            
            # 创建通道
            channel = await connection.channel()
            await channel.close()
            await connection.close()
            
            return True
            
        except Exception as e:
            self.errors.append(f"RabbitMQ连接失败: {str(e)}")
            return False
    
    async def verify_chromadb(self) -> bool:
        """验证ChromaDB连接"""
        try:
            import chromadb
            
            # 尝试连接ChromaDB
            client = chromadb.HttpClient(host="localhost", port=8000)
            
            # 获取版本信息
            version = client.get_version()
            
            if not version:
                self.errors.append("ChromaDB版本信息获取失败")
                return False
            
            return True
            
        except Exception as e:
            self.errors.append(f"ChromaDB连接失败: {str(e)}")
            return False
    
    async def verify_backend_api(self) -> bool:
        """验证后端API"""
        try:
            async with aiohttp.ClientSession() as session:
                # 检查健康检查端点
                async with session.get("http://localhost:8000/health") as response:
                    if response.status != 200:
                        self.errors.append(f"后端API健康检查失败: {response.status}")
                        return False
                    
                    data = await response.json()
                    if data.get("status") != "healthy":
                        self.errors.append("后端API状态不健康")
                        return False
                
                # 检查API文档
                async with session.get("http://localhost:8000/docs") as response:
                    if response.status != 200:
                        self.errors.append("API文档访问失败")
                        return False
            
            return True
            
        except Exception as e:
            self.errors.append(f"后端API验证失败: {str(e)}")
            return False
    
    async def verify_frontend(self) -> bool:
        """验证前端应用"""
        try:
            async with aiohttp.ClientSession() as session:
                # 检查前端应用
                async with session.get("http://localhost:3000") as response:
                    if response.status != 200:
                        self.errors.append(f"前端应用访问失败: {response.status}")
                        return False
            
            return True
            
        except Exception as e:
            self.errors.append(f"前端应用验证失败: {str(e)}")
            return False
    
    async def verify_cicd(self) -> bool:
        """验证CI/CD配置"""
        try:
            # 检查GitHub Actions配置文件
            workflows_dir = Path(".github/workflows")
            if not workflows_dir.exists():
                self.errors.append("GitHub Actions配置目录不存在")
                return False
            
            required_workflows = ["ci.yml", "code-quality.yml"]
            for workflow in required_workflows:
                if not (workflows_dir / workflow).exists():
                    self.errors.append(f"缺少工作流配置: {workflow}")
                    return False
            
            # 检查Docker配置
            docker_files = ["Dockerfile.backend", "Dockerfile.frontend", "docker-compose.yml"]
            for docker_file in docker_files:
                if not Path(docker_file).exists():
                    self.errors.append(f"缺少Docker配置: {docker_file}")
                    return False
            
            return True
            
        except Exception as e:
            self.errors.append(f"CI/CD配置验证失败: {str(e)}")
            return False
    
    def display_results(self):
        """显示验证结果"""
        # 创建结果表格
        table = Table(title="环境验证结果")
        table.add_column("检查项", style="cyan", no_wrap=True)
        table.add_column("状态", style="magenta")
        table.add_column("说明", style="green")
        
        for check, result in self.results.items():
            status = "✅ 通过" if result else "❌ 失败"
            description = "正常" if result else "需要修复"
            table.add_row(check, status, description)
        
        console.print(table)
        
        # 显示错误信息
        if self.errors:
            console.print("\n[bold red]发现的问题:[/bold red]")
            for error in self.errors:
                console.print(f"  • {error}")
        
        # 显示总结
        passed = sum(self.results.values())
        total = len(self.results)
        
        if passed == total:
            console.print(Panel.fit(
                f"🎉 环境验证完成！所有 {total} 项检查都通过了。",
                style="bold green"
            ))
        else:
            console.print(Panel.fit(
                f"⚠️  环境验证完成！{passed}/{total} 项检查通过，请修复失败的项目。",
                style="bold yellow"
            ))


async def main():
    """主函数"""
    verifier = EnvironmentVerifier()
    success = await verifier.verify_all()
    
    if success:
        console.print("\n[bold green]✅ 环境验证成功！开发环境已准备就绪。[/bold green]")
        sys.exit(0)
    else:
        console.print("\n[bold red]❌ 环境验证失败！请修复上述问题后重试。[/bold red]")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
