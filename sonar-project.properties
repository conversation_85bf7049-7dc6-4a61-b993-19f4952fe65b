# SonarCloud配置文件
# 用于代码质量分析

# 项目基本信息
sonar.projectKey=chaiguanjia
sonar.organization=your-org
sonar.projectName=柴管家
sonar.projectVersion=1.0.0

# 源代码路径
sonar.sources=chaiguanjia,src/Host,frontend/src
sonar.tests=tests,frontend/src/__tests__

# 排除文件
sonar.exclusions=**/node_modules/**,**/dist/**,**/build/**,**/*.min.js,**/migrations/**,**/venv/**,**/__pycache__/**

# 测试排除
sonar.test.exclusions=**/node_modules/**,**/dist/**,**/build/**

# 语言特定配置
sonar.python.coverage.reportPaths=coverage.xml
sonar.javascript.lcov.reportPaths=frontend/coverage/lcov.info

# Python特定配置
sonar.python.xunit.reportPath=test-results.xml

# JavaScript/TypeScript特定配置
sonar.typescript.tsconfigPath=frontend/tsconfig.json

# 代码重复检测
sonar.cpd.exclusions=**/migrations/**,**/tests/**

# 分析参数
sonar.sourceEncoding=UTF-8
