"""
柴管家主应用入口
"""
import logging
from contextlib import asynccontextmanager
from typing import AsyncGenerator

import structlog
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware

from chaiguanjia.core.config import get_settings
from chaiguanjia.core.logging import configure_logging
from chaiguanjia.core.middleware import (
    LoggingMiddleware,
    ProcessTimeMiddleware,
    RateLimitMiddleware,
)
from chaiguanjia.api import api_router


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """应用生命周期管理"""
    settings = get_settings()
    logger = structlog.get_logger()
    
    # 启动时初始化
    logger.info("柴管家应用启动中...", version=settings.app_version)
    
    # 初始化数据库连接
    from chaiguanjia.core.database import init_database
    await init_database()
    
    # 初始化消息队列
    from chaiguanjia.core.message_queue import init_message_queue
    await init_message_queue()
    
    # 初始化向量数据库
    from chaiguanjia.core.vector_db import init_vector_db
    await init_vector_db()
    
    logger.info("柴管家应用启动完成")
    
    yield
    
    # 关闭时清理
    logger.info("柴管家应用关闭中...")
    
    # 清理数据库连接
    from chaiguanjia.core.database import close_database
    await close_database()
    
    # 清理消息队列连接
    from chaiguanjia.core.message_queue import close_message_queue
    await close_message_queue()
    
    logger.info("柴管家应用已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    settings = get_settings()
    
    # 配置日志
    configure_logging(settings.log_level, settings.log_format)
    
    # 创建应用实例
    app = FastAPI(
        title="柴管家 API",
        description="私域运营解决方案 - 聚合多平台消息和AI能力",
        version=settings.app_version,
        docs_url="/docs" if settings.enable_swagger else None,
        redoc_url="/redoc" if settings.enable_redoc else None,
        openapi_url="/openapi.json" if settings.enable_swagger else None,
        lifespan=lifespan,
    )
    
    # 配置中间件
    setup_middleware(app, settings)
    
    # 注册路由
    setup_routes(app)
    
    return app


def setup_middleware(app: FastAPI, settings) -> None:
    """配置中间件"""
    
    # 信任主机中间件
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=["*"] if settings.app_env == "development" else settings.allowed_hosts
    )
    
    # CORS 中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=settings.cors_allow_credentials,
        allow_methods=settings.cors_allow_methods,
        allow_headers=settings.cors_allow_headers,
    )
    
    # 自定义中间件
    app.add_middleware(ProcessTimeMiddleware)
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(RateLimitMiddleware)


def setup_routes(app: FastAPI) -> None:
    """注册路由"""
    
    # 健康检查
    @app.get("/health")
    async def health_check():
        """健康检查端点"""
        return {
            "status": "healthy",
            "service": "chaiguanjia",
            "version": get_settings().app_version
        }
    
    # 注册API路由
    app.include_router(api_router)


# 创建应用实例
app = create_app()


if __name__ == "__main__":
    settings = get_settings()
    
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.app_env == "development",
        log_level=settings.log_level.lower(),
        access_log=True,
    )
