[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "chaiguanjia"
version = "1.0.0"
description = "柴管家 - 私域运营解决方案"
readme = "../../README.md"
license = {file = "LICENSE"}
authors = [
    {name = "AI开发团队", email = "<EMAIL>"},
]
maintainers = [
    {name = "AI开发团队", email = "<EMAIL>"},
]
keywords = ["fastapi", "ai", "private-domain", "messaging"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Framework :: FastAPI",
    "Topic :: Internet :: WWW/HTTP :: HTTP Servers",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
]
requires-python = ">=3.11"
dependencies = [
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "sqlalchemy>=2.0.23",
    "alembic>=1.13.1",
    "psycopg2-binary>=2.9.9",
    "asyncpg>=0.29.0",
    "chromadb>=0.4.18",
    "pika>=1.3.2",
    "redis>=5.0.1",
    "httpx>=0.25.2",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "python-multipart>=0.0.6",
    "openai>=1.3.7",
    "python-dotenv>=1.0.0",
    "structlog>=23.2.0",
    "rich>=13.7.0",
]

[project.optional-dependencies]
dev = [
    "black>=23.11.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.7.1",
    "pre-commit>=3.6.0",
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.12.0",
    "factory-boy>=3.3.0",
]
docs = [
    "mkdocs>=1.5.3",
    "mkdocs-material>=9.4.8",
]
monitoring = [
    "sentry-sdk[fastapi]>=1.38.0",
    "prometheus-client>=0.19.0",
]

[project.urls]
Homepage = "https://github.com/chaiguanjia/chaiguanjia"
Documentation = "https://docs.chaiguanjia.com"
Repository = "https://github.com/chaiguanjia/chaiguanjia.git"
"Bug Tracker" = "https://github.com/chaiguanjia/chaiguanjia/issues"

[project.scripts]
chaiguanjia = "chaiguanjia.main:app"

[tool.hatch.build.targets.wheel]
packages = ["chaiguanjia"]

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["chaiguanjia"]
known_third_party = ["fastapi", "pydantic", "sqlalchemy"]

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "chromadb.*",
    "pika.*",
    "redis.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = [
    "../../tests",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
python_classes = [
    "Test*",
]
python_functions = [
    "test_*",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "e2e: marks tests as end-to-end tests",
]

[tool.coverage.run]
source = ["chaiguanjia"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.flake8]
max-line-length = 88
extend-ignore = ["E203", "W503"]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".eggs",
    "*.egg-info",
    ".venv",
    ".tox",
]
