# FastAPI 核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0

# 数据库依赖
sqlalchemy==2.0.23
alembic==1.13.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# 向量数据库
chromadb==0.4.18

# 消息队列
aio-pika==9.4.1
celery==5.3.4

# Redis (缓存)
redis==5.0.1
hiredis==2.2.3

# HTTP 客户端
httpx==0.25.2
aiohttp==3.9.1

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# AI 和机器学习
openai>=1.6.1,<2.0.0
langchain==0.0.350
langchain-openai==0.0.2
tiktoken==0.5.2

# 工具库
python-dotenv==1.0.0
structlog==23.2.0
rich==13.7.0
typer==0.9.0

# 数据验证和序列化
marshmallow==3.20.2
email-validator==2.1.0

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 文件处理
pillow==10.1.0
python-magic==0.4.27

# 监控和日志
sentry-sdk[fastapi]==1.38.0
prometheus-client==0.19.0

# 开发工具
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.6.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
httpx==0.25.2
factory-boy==3.3.0

# API 文档
fastapi[all]==0.104.1

# 中间件
slowapi==0.1.9
starlette-prometheus==0.9.0

# 配置管理
dynaconf==3.2.4

# 数据迁移
yoyo-migrations==8.2.0

# 异步任务
dramatiq[redis]==1.15.0

# 文档生成
mkdocs==1.5.3
mkdocs-material==9.4.8
