import axios from 'axios'

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证 token
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳
    config.metadata = { startTime: new Date() }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // 计算请求耗时
    const endTime = new Date()
    const duration = endTime.getTime() - response.config.metadata?.startTime?.getTime()
    
    console.log(`API请求耗时: ${duration}ms - ${response.config.url}`)
    
    return response
  },
  (error) => {
    // 统一错误处理
    if (error.response?.status === 401) {
      // 清除本地存储的认证信息
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      
      // 重定向到登录页
      window.location.href = '/login'
    }
    
    // 显示错误消息
    const message = error.response?.data?.message || error.message || '请求失败'
    console.error('API请求错误:', message)
    
    return Promise.reject(error)
  }
)

export default apiClient
