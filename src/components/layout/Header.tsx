import React from 'react'
import { 
  But<PERSON>, 
  Dropdown, 
  Dropdown<PERSON>rigger, 
  DropdownMenu, 
  DropdownItem,
  Avatar,
  Badge
} from '@heroui/react'
import { Bell, Search, User, LogOut } from 'lucide-react'

const Header: React.FC = () => {
  return (
    <header className="h-16 bg-white dark:bg-gray-900 border-b border-divider flex items-center justify-between px-6">
      {/* 搜索栏 */}
      <div className="flex-1 max-w-md">
        <div className="relative">
          <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-default-400" />
          <input
            type="text"
            placeholder="搜索消息、联系人..."
            className="w-full pl-10 pr-4 py-2 bg-default-100 border border-transparent rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
          />
        </div>
      </div>

      {/* 右侧操作区 */}
      <div className="flex items-center space-x-4">
        {/* 通知 */}
        <Button
          isIconOnly
          variant="light"
          className="relative"
        >
          <Badge content="3" color="danger" size="sm">
            <Bell size={18} />
          </Badge>
        </Button>

        {/* 用户菜单 */}
        <Dropdown placement="bottom-end">
          <DropdownTrigger>
            <Button
              variant="light"
              className="p-0 min-w-0 h-auto"
            >
              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <p className="text-sm font-medium text-foreground">管理员</p>
                  <p className="text-xs text-default-500"><EMAIL></p>
                </div>
                <Avatar
                  size="sm"
                  src=""
                  fallback={<User size={16} />}
                />
              </div>
            </Button>
          </DropdownTrigger>
          <DropdownMenu aria-label="用户菜单">
            <DropdownItem key="profile" startContent={<User size={16} />}>
              个人资料
            </DropdownItem>
            <DropdownItem key="settings" startContent={<Bell size={16} />}>
              通知设置
            </DropdownItem>
            <DropdownItem 
              key="logout" 
              color="danger" 
              startContent={<LogOut size={16} />}
            >
              退出登录
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </div>
    </header>
  )
}

export default Header
