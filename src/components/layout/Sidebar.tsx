import React from 'react'
import { Link, useLocation } from 'react-router-dom'
import { 
  LayoutDashboard, 
  MessageSquare, 
  Zap, 
  Brain, 
  Settings,
  ChevronRight
} from 'lucide-react'
import { cn } from '@/utils/cn'

const navigation = [
  {
    name: '仪表板',
    href: '/',
    icon: LayoutDashboard,
  },
  {
    name: '渠道管理',
    href: '/channels',
    icon: Zap,
  },
  {
    name: '消息工作台',
    href: '/messages',
    icon: MessageSquare,
  },
  {
    name: 'AI助手',
    href: '/ai-assistant',
    icon: Brain,
  },
  {
    name: '系统设置',
    href: '/settings',
    icon: Settings,
  },
]

const Sidebar: React.FC = () => {
  const location = useLocation()

  return (
    <div className="w-64 bg-white dark:bg-gray-900 border-r border-divider flex flex-col">
      {/* Logo */}
      <div className="p-6 border-b border-divider">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">柴</span>
          </div>
          <div>
            <h1 className="text-lg font-bold text-foreground">柴管家</h1>
            <p className="text-xs text-default-500">私域运营平台</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 p-4 space-y-2">
        {navigation.map((item) => {
          const isActive = location.pathname === item.href
          const Icon = item.icon

          return (
            <Link
              key={item.name}
              to={item.href}
              className={cn(
                'flex items-center space-x-3 px-3 py-2.5 rounded-lg text-sm font-medium transition-colors',
                isActive
                  ? 'bg-primary text-white'
                  : 'text-default-600 hover:bg-default-100 hover:text-foreground'
              )}
            >
              <Icon size={18} />
              <span>{item.name}</span>
              {isActive && (
                <ChevronRight size={16} className="ml-auto" />
              )}
            </Link>
          )
        })}
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-divider">
        <div className="text-xs text-default-400 text-center">
          版本 1.0.0
        </div>
      </div>
    </div>
  )
}

export default Sidebar
