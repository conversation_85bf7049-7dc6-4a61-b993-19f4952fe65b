import React from 'react'
import { <PERSON>, CardBody, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@heroui/react'
import { Brain, Plus } from 'lucide-react'

const AIAssistant: React.FC = () => {
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-foreground">AI助手</h1>
          <p className="text-default-500 mt-2">管理知识库和AI回复策略</p>
        </div>
        <Button color="primary" startContent={<Plus size={16} />}>
          添加知识条目
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">知识库统计</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-default-500">问答对数量</span>
                <span className="font-semibold">0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-default-500">分类数量</span>
                <span className="font-semibold">0</span>
              </div>
              <div className="flex justify-between">
                <span className="text-default-500">平均置信度</span>
                <span className="font-semibold">0%</span>
              </div>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">AI性能</h3>
          </CardHeader>
          <CardBody>
            <div className="space-y-4">
              <div className="flex justify-between">
                <span className="text-default-500">自动回复率</span>
                <span className="font-semibold">0%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-default-500">用户满意度</span>
                <span className="font-semibold">0%</span>
              </div>
              <div className="flex justify-between">
                <span className="text-default-500">响应时间</span>
                <span className="font-semibold">0ms</span>
              </div>
            </div>
          </CardBody>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <h3 className="text-lg font-semibold">知识库管理</h3>
        </CardHeader>
        <CardBody>
          <div className="flex flex-col items-center justify-center py-12">
            <Brain size={48} className="text-default-300 mb-4" />
            <h3 className="text-lg font-semibold mb-2">知识库为空</h3>
            <p className="text-default-500 text-center mb-4">
              开始添加问答对来训练您的AI助手
            </p>
            <Button color="primary" startContent={<Plus size={16} />}>
              添加第一个问答对
            </Button>
          </div>
        </CardBody>
      </Card>
    </div>
  )
}

export default AIAssistant
