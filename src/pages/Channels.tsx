import React from 'react'
import { <PERSON>, CardBody, CardHeader, But<PERSON> } from '@heroui/react'
import { Plus } from 'lucide-react'

const Channels: React.FC = () => {
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold text-foreground">渠道管理</h1>
          <p className="text-default-500 mt-2">管理您的社交媒体渠道连接</p>
        </div>
        <Button color="primary" startContent={<Plus size={16} />}>
          添加渠道
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="border-dashed border-2 border-default-200">
          <CardBody className="flex flex-col items-center justify-center py-12">
            <Plus size={48} className="text-default-300 mb-4" />
            <h3 className="text-lg font-semibold mb-2">添加新渠道</h3>
            <p className="text-default-500 text-center">
              连接微信、QQ、抖音等社交平台
            </p>
            <Button color="primary" className="mt-4">
              开始连接
            </Button>
          </CardBody>
        </Card>
      </div>
    </div>
  )
}

export default Channels
