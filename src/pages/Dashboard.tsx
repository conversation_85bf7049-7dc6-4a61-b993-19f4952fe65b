import React from 'react'
import { Card, CardBody, CardHeader } from '@heroui/react'

const Dashboard: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-foreground">仪表板</h1>
        <p className="text-default-500 mt-2">欢迎使用柴管家私域运营平台</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <h3 className="text-sm font-medium text-default-500">总渠道数</h3>
          </CardHeader>
          <CardBody className="pt-0">
            <p className="text-2xl font-bold">0</p>
          </CardBody>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <h3 className="text-sm font-medium text-default-500">活跃会话</h3>
          </CardHeader>
          <CardBody className="pt-0">
            <p className="text-2xl font-bold">0</p>
          </CardBody>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <h3 className="text-sm font-medium text-default-500">今日消息</h3>
          </CardHeader>
          <CardBody className="pt-0">
            <p className="text-2xl font-bold">0</p>
          </CardBody>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <h3 className="text-sm font-medium text-default-500">AI回复率</h3>
          </CardHeader>
          <CardBody className="pt-0">
            <p className="text-2xl font-bold">0%</p>
          </CardBody>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">最近活动</h3>
          </CardHeader>
          <CardBody>
            <p className="text-default-500">暂无活动记录</p>
          </CardBody>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">快速操作</h3>
          </CardHeader>
          <CardBody>
            <p className="text-default-500">功能开发中...</p>
          </CardBody>
        </Card>
      </div>
    </div>
  )
}

export default Dashboard
