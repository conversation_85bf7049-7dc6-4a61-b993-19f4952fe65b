import React from 'react'
import { Card, CardBody, CardHeader } from '@heroui/react'
import { MessageCircle } from 'lucide-react'

const Messages: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-foreground">消息工作台</h1>
        <p className="text-default-500 mt-2">统一管理所有渠道的消息对话</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
        {/* 会话列表 */}
        <Card className="lg:col-span-1">
          <CardHeader>
            <h3 className="text-lg font-semibold">会话列表</h3>
          </CardHeader>
          <CardBody>
            <div className="flex flex-col items-center justify-center py-12">
              <MessageCircle size={48} className="text-default-300 mb-4" />
              <p className="text-default-500 text-center">
                暂无活跃会话
              </p>
            </div>
          </CardBody>
        </Card>

        {/* 消息区域 */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <h3 className="text-lg font-semibold">消息详情</h3>
          </CardHeader>
          <CardBody>
            <div className="flex flex-col items-center justify-center py-12">
              <MessageCircle size={48} className="text-default-300 mb-4" />
              <p className="text-default-500 text-center">
                选择一个会话开始聊天
              </p>
            </div>
          </CardBody>
        </Card>
      </div>
    </div>
  )
}

export default Messages
