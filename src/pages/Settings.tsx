import React from 'react'
import { Card, CardBody, CardHeader, Switch, But<PERSON> } from '@heroui/react'
import { Save } from 'lucide-react'

const Settings: React.FC = () => {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-foreground">系统设置</h1>
        <p className="text-default-500 mt-2">配置系统参数和用户偏好</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">通知设置</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">新消息通知</p>
                <p className="text-sm text-default-500">接收新消息时显示通知</p>
              </div>
              <Switch defaultSelected />
            </div>
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">邮件通知</p>
                <p className="text-sm text-default-500">重要事件邮件提醒</p>
              </div>
              <Switch />
            </div>
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">声音提醒</p>
                <p className="text-sm text-default-500">消息到达时播放提示音</p>
              </div>
              <Switch defaultSelected />
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">AI设置</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">自动回复</p>
                <p className="text-sm text-default-500">启用AI自动回复功能</p>
              </div>
              <Switch />
            </div>
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">学习模式</p>
                <p className="text-sm text-default-500">AI从对话中学习优化</p>
              </div>
              <Switch defaultSelected />
            </div>
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">置信度阈值</p>
                <p className="text-sm text-default-500">低于阈值时转人工</p>
              </div>
              <span className="text-sm font-medium">85%</span>
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">界面设置</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">深色模式</p>
                <p className="text-sm text-default-500">使用深色主题界面</p>
              </div>
              <Switch />
            </div>
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">紧凑布局</p>
                <p className="text-sm text-default-500">减少界面间距</p>
              </div>
              <Switch />
            </div>
          </CardBody>
        </Card>

        <Card>
          <CardHeader>
            <h3 className="text-lg font-semibold">数据管理</h3>
          </CardHeader>
          <CardBody className="space-y-4">
            <Button color="primary" variant="flat" className="w-full">
              导出数据
            </Button>
            <Button color="warning" variant="flat" className="w-full">
              清理缓存
            </Button>
            <Button color="danger" variant="flat" className="w-full">
              重置设置
            </Button>
          </CardBody>
        </Card>
      </div>

      <div className="mt-6 flex justify-end">
        <Button color="primary" startContent={<Save size={16} />}>
          保存设置
        </Button>
      </div>
    </div>
  )
}

export default Settings
