// API 响应基础类型
export interface ApiResponse<T = any> {
  status: 'success' | 'error'
  data?: T
  message: string
  code?: number
}

// 分页响应类型
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  avatar?: string
  role: 'admin' | 'user'
  createdAt: string
  updatedAt: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: User
}

// 渠道相关类型
export interface Channel {
  id: string
  name: string
  type: 'wechat' | 'qq' | 'douyin' | 'xiaohongshu' | 'weibo'
  status: 'connected' | 'disconnected' | 'error'
  config: Record<string, any>
  lastSync?: string
  createdAt: string
  updatedAt: string
}

export interface CreateChannelRequest {
  name: string
  type: Channel['type']
  config: Record<string, any>
}

// 消息相关类型
export interface Message {
  id: string
  conversationId: string
  channelId: string
  senderId: string
  content: string
  type: 'text' | 'image' | 'video' | 'audio' | 'file'
  direction: 'inbound' | 'outbound'
  status: 'sent' | 'delivered' | 'read' | 'failed'
  metadata?: Record<string, any>
  createdAt: string
}

export interface Conversation {
  id: string
  channelId: string
  participantId: string
  participantName: string
  participantAvatar?: string
  lastMessage?: Message
  unreadCount: number
  status: 'active' | 'archived' | 'blocked'
  createdAt: string
  updatedAt: string
}

export interface SendMessageRequest {
  conversationId: string
  content: string
  type: Message['type']
}

// AI 助手相关类型
export interface KnowledgeEntry {
  id: string
  question: string
  answer: string
  category: string
  tags: string[]
  confidence: number
  isActive: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateKnowledgeEntryRequest {
  question: string
  answer: string
  category: string
  tags: string[]
}

export interface IntentAnalysisRequest {
  message: string
  context?: Record<string, any>
}

export interface IntentAnalysisResponse {
  intent: string
  confidence: number
  entities: Array<{
    type: string
    value: string
    confidence: number
  }>
}

export interface GenerateResponseRequest {
  message: string
  conversationId: string
  context?: Record<string, any>
}

export interface GenerateResponseResponse {
  response: string
  confidence: number
  sources: Array<{
    id: string
    type: 'knowledge_base' | 'template'
    content: string
    relevance: number
  }>
}
