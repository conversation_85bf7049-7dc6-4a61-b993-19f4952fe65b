"""
主应用测试
"""

import pytest
from fastapi.testclient import TestClient

from src.Host.main import app

client = TestClient(app)


def test_health_check():
    """测试健康检查端点"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "version" in data


def test_root_endpoint():
    """测试根端点"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data


def test_api_docs():
    """测试API文档端点"""
    response = client.get("/docs")
    assert response.status_code == 200


def test_openapi_schema():
    """测试OpenAPI模式"""
    response = client.get("/openapi.json")
    assert response.status_code == 200
    data = response.json()
    assert "openapi" in data
    assert "info" in data
