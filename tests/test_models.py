"""
数据模型测试
"""

import pytest
from datetime import datetime
from uuid import uuid4

from chaiguanjia.core.models import BaseModel, BaseSchema


class TestUser(BaseModel):
    """测试用户模型"""
    __tablename__ = "test_users"
    
    username: str
    email: str


class TestUserSchema(BaseSchema):
    """测试用户模式"""
    username: str
    email: str


def test_base_model_creation():
    """测试基础模型创建"""
    user = TestUser(
        username="testuser",
        email="<EMAIL>"
    )
    
    assert user.username == "testuser"
    assert user.email == "<EMAIL>"
    assert user.id is not None
    assert user.created_at is not None
    assert user.updated_at is not None
    assert user.is_deleted is False


def test_base_schema_validation():
    """测试基础模式验证"""
    schema = TestUserSchema(
        username="testuser",
        email="<EMAIL>"
    )
    
    assert schema.username == "testuser"
    assert schema.email == "<EMAIL>"


def test_base_schema_invalid_email():
    """测试无效邮箱验证"""
    with pytest.raises(ValueError):
        TestUserSchema(
            username="testuser",
            email="invalid-email"
        )
