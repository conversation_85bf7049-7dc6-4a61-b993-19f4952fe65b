"""
服务层测试
"""

import pytest
from unittest.mock import AsyncMock, MagicMock
from datetime import datetime
from uuid import uuid4

from chaiguanjia.services.message_service import MessageService, MessageData
from chaiguanjia.services.notification_service import NotificationService, NotificationData


@pytest.fixture
def mock_message_queue():
    """模拟消息队列"""
    mock = MagicMock()
    mock.publish_command = AsyncMock()
    mock.publish_event = AsyncMock()
    mock.subscribe_event = AsyncMock()
    return mock


@pytest.fixture
def message_service(mock_message_queue):
    """消息服务实例"""
    return MessageService(mock_message_queue)


@pytest.fixture
def notification_service(mock_message_queue):
    """通知服务实例"""
    return NotificationService(mock_message_queue)


@pytest.mark.asyncio
async def test_send_message(message_service, mock_message_queue):
    """测试发送消息"""
    message_data = MessageData(
        id=uuid4(),
        channel_id=uuid4(),
        content="测试消息",
        message_type="text",
        status="pending",
        created_at=datetime.utcnow()
    )
    
    result = await message_service.send_message(message_data)
    
    assert result is True
    mock_message_queue.publish_command.assert_called_once()


@pytest.mark.asyncio
async def test_schedule_message(message_service, mock_message_queue):
    """测试定时发送消息"""
    message_data = MessageData(
        id=uuid4(),
        channel_id=uuid4(),
        content="定时消息",
        message_type="text",
        status="scheduled",
        created_at=datetime.utcnow()
    )
    
    scheduled_at = datetime.utcnow()
    result = await message_service.schedule_message(message_data, scheduled_at)
    
    assert result is True
    mock_message_queue.publish_command.assert_called_once()


@pytest.mark.asyncio
async def test_send_notification(notification_service, mock_message_queue):
    """测试发送通知"""
    notification_data = NotificationData(
        id=uuid4(),
        user_id=uuid4(),
        title="测试通知",
        content="这是一个测试通知",
        notification_type="info",
        priority="medium",
        channels=["in_app"],
        created_at=datetime.utcnow()
    )
    
    result = await notification_service.send_notification(notification_data)
    
    assert result is True
    mock_message_queue.publish_event.assert_called_once()


@pytest.mark.asyncio
async def test_send_system_notification(notification_service, mock_message_queue):
    """测试发送系统通知"""
    result = await notification_service.send_system_notification(
        title="系统通知",
        content="系统维护通知",
        notification_type="warning",
        priority="high"
    )
    
    assert result is True
    mock_message_queue.publish_event.assert_called_once()


@pytest.mark.asyncio
async def test_send_user_notification(notification_service, mock_message_queue):
    """测试发送用户通知"""
    user_id = uuid4()
    result = await notification_service.send_user_notification(
        user_id=user_id,
        title="用户通知",
        content="您有新消息",
        notification_type="info"
    )
    
    assert result is True
    mock_message_queue.publish_event.assert_called_once()
