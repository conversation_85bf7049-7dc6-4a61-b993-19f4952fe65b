# GitHub Projects自动化系统完整指南

## 目录
- [1. 项目背景与目标](#1-项目背景与目标)
- [2. 技术架构总结](#2-技术架构总结)
- [3. 关键技术问题与解决方案](#3-关键技术问题与解决方案)
- [4. 实施指南](#4-实施指南)
- [5. 团队协作规范](#5-团队协作规范)
- [6. 经验教训与最佳实践](#6-经验教训与最佳实践)

---

## 1. 项目背景与目标

### 1.1 项目概述

"柴管家"项目是一个基于GitHub的开发项目，需要实现高效的任务管理和项目协作。为了提升开发效率和项目管理的自动化程度，我们开发了一套完整的GitHub Projects自动化系统。

### 1.2 核心需求

**主要目标：**
- 使GitHub Projects成为项目管理的**唯一真实信息源**
- 实现Issue创建、状态更新的全自动化管理
- 支持多种触发机制（手动、定时、事件驱动）
- 提供完整的任务生命周期管理

**具体功能要求：**
1. **自动任务选择**：从"待办"列自动选择最高优先级任务
2. **自动状态更新**：
   - Issue创建时自动添加到项目并设为"待办"
   - 任务开始时自动移至"进行中"
   - 任务完成时自动移至"已完成"
3. **多触发支持**：手动触发、定时触发、GitHub事件触发

### 1.3 技术选型说明

**选择个人GitHub Projects的原因：**
- 项目所有者为个人账户（Amoresdk）
- 个人项目提供足够的功能满足需求
- 避免组织级别的权限复杂性
- 降低配置和维护成本

---

## 2. 技术架构总结

### 2.1 核心技术栈

```mermaid
graph TD
    A[GitHub Events] --> B[GitHub Actions Workflow]
    B --> C[GraphQL API]
    C --> D[GitHub Projects V2]
    B --> E[Bash Scripts]
    E --> F[Task Automation]
    
    G[Personal Access Token] --> C
    H[Project Configuration] --> E
```

### 2.2 GitHub Projects V2 关键概念

**项目结构：**
- **Project**: 项目容器，包含多个视图和字段
- **Items**: 项目中的条目（Issue、PR等）
- **Fields**: 自定义字段（Status、Priority等）
- **Views**: 不同的项目视图（看板、表格等）

**重要API端点：**
```graphql
# 查询个人项目
query($owner: String!, $number: Int!) {
  user(login: $owner) {
    projectV2(number: $number) {
      id
      title
      fields(first: 20) { ... }
    }
  }
}

# 添加Issue到项目
mutation($projectId: ID!, $contentId: ID!) {
  addProjectV2ItemById(input: {
    projectId: $projectId
    contentId: $contentId
  }) {
    item { id }
  }
}
```

### 2.3 认证方式对比

| 认证方式 | 个人项目支持 | 组织项目支持 | 权限粒度 | 配置复杂度 |
|---------|------------|------------|----------|-----------|
| Personal Access Token | ✅ 完全支持 | ✅ 支持 | 粗粒度 | 简单 |
| GitHub App | ⚠️ 有限支持 | ✅ 完全支持 | 细粒度 | 复杂 |

**最终选择：Personal Access Token**
- 个人项目的最佳选择
- 配置简单，维护成本低
- 权限足够满足需求

### 2.4 个人项目 vs 组织项目 API差异

**关键差异点：**

| 项目类型 | GraphQL查询根节点 | 示例 |
|---------|------------------|------|
| 个人项目 | `user(login: $owner)` | `user(login: "Amoresdk")` |
| 组织项目 | `organization(login: $owner)` | `organization(login: "MyOrg")` |

**权限差异：**
- 个人项目：通过PAT的repo和project权限控制
- 组织项目：需要组织级别的权限配置

---

## 3. 关键技术问题与解决方案

### 3.1 GraphQL查询错误问题

**问题1：未使用的变量声明**
```
Variable $issueNumber is declared but not used
```

**原因：** GraphQL查询中声明了变量但未在查询体中使用

**解决方案：**
```graphql
# 错误写法
query($owner: String!, $number: Int!, $issueNumber: Int!) {
  user(login: $owner) { ... }
}

# 正确写法
query($owner: String!, $number: Int!) {
  user(login: $owner) { ... }
}
```

**问题2：null值处理错误**
```
Cannot iterate over null
```

**原因：** jq查询时未处理可能的null值

**解决方案：**
```bash
# 错误写法
jq -r '.data.user.projectV2.items.nodes[] | select(...)'

# 正确写法
jq -r '.data.user.projectV2.items.nodes[]? | select(...)'
```

### 3.2 GraphQL Mutation名称错误

**问题：** 使用了错误的mutation名称
```
Field 'addProjectV2ItemByContentId' doesn't exist
```

**原因：** GitHub官方文档中的mutation名称已更新

**解决方案：**
```graphql
# 错误的mutation名称
addProjectV2ItemByContentId

# 正确的mutation名称
addProjectV2ItemById
```

### 3.3 jq提取路径不匹配

**问题：** 修改了mutation名称但未同步更新jq提取路径

**解决方案：**
```bash
# 错误写法（路径与mutation不匹配）
item_id=$(echo "$add_result" | jq -r '.data.addProjectV2ItemByContentId.item.id')

# 正确写法
item_id=$(echo "$add_result" | jq -r '.data.addProjectV2ItemById.item.id')
```

### 3.4 GitHub Secret命名限制

**问题：** Secret名称不能以"GITHUB_"开头
```
Secret names cannot start with GITHUB_
```

**解决方案：**
```yaml
# 错误命名
GITHUB_PAT

# 正确命名
CHAIGUANJIA_PAT
```

### 3.5 项目类型识别错误

**问题：** 错误地将个人项目当作组织项目处理

**症状：**
- 404错误访问组织项目
- GraphQL查询返回null

**解决方案：**
1. 确认项目URL格式：
   - 个人项目：`https://github.com/users/{username}/projects/{number}`
   - 组织项目：`https://github.com/orgs/{orgname}/projects/{number}`

2. 使用正确的GraphQL查询根节点

---

## 4. 实施指南

### 4.1 前置条件检查

**必需条件：**
- [ ] GitHub个人账户
- [ ] 已创建GitHub Projects V2项目
- [ ] 项目包含Status字段，且有Todo、In Progress、Done选项
- [ ] 仓库具有Actions权限

### 4.2 Personal Access Token配置

**步骤1：创建PAT**
1. 访问 GitHub Settings > Developer settings > Personal access tokens
2. 点击"Generate new token (classic)"
3. 设置权限：
   - `repo` (完整仓库权限)
   - `project` (项目权限)
4. 复制生成的token

**步骤2：配置Repository Secret**
1. 进入仓库 Settings > Secrets and variables > Actions
2. 点击"New repository secret"
3. Name: `CHAIGUANJIA_PAT`
4. Value: 粘贴PAT token

### 4.3 项目配置文件

**创建 `.github/project-config.json`：**
```json
{
  "project": {
    "owner": "Amoresdk",
    "type": "user",
    "number": 1,
    "name": "柴管家开发任务看板",
    "description": "柴管家项目的主要开发任务管理看板"
  },
  "fields": {
    "status": {
      "name": "Status",
      "options": {
        "todo": "Todo",
        "in_progress": "In Progress", 
        "done": "Done"
      }
    }
  },
  "automation": {
    "auto_assign": true,
    "auto_status_update": true,
    "conflict_resolution": "skip"
  }
}
```

### 4.4 核心Workflow文件

**主要文件结构：**
```
.github/
├── workflows/
│   ├── project-automation.yml    # Issue/PR自动同步
│   └── task-automation.yml       # 任务管理自动化
├── project-config.json           # 项目配置
└── scripts/
    ├── start-next-task.sh        # 开始下一个任务
    ├── complete-task.sh          # 完成当前任务
    ├── sync-status.sh            # 同步状态
    └── show-status.sh            # 显示状态
```

**project-automation.yml 核心配置：**
```yaml
name: 柴管家项目看板自动化

on:
  issues:
    types: [opened, closed]
  pull_request:
    types: [opened, closed, merged]

env:
  PROJECT_OWNER: Amoresdk
  PROJECT_NUMBER: 1
  PROJECT_NAME: "柴管家开发任务看板"

jobs:
  sync-to-project:
    name: 同步到项目看板
    runs-on: ubuntu-latest
    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置访问令牌
        id: app-token
        run: |
          if [ -n "${{ secrets.CHAIGUANJIA_PAT }}" ]; then
            echo "token=${{ secrets.CHAIGUANJIA_PAT }}" >> $GITHUB_OUTPUT
            echo "✅ 使用Personal Access Token进行认证"
          else
            echo "❌ 错误：未找到CHAIGUANJIA_PAT Secret"
            exit 1
          fi

      - name: 获取项目配置
        id: project-config
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
        run: |
          # 查询个人项目信息
          project_data=$(gh api graphql -f query='
            query($owner: String!, $number: Int!) {
              user(login: $owner) {
                projectV2(number: $number) {
                  id
                  title
                  fields(first: 20) {
                    nodes {
                      ... on ProjectV2Field {
                        id
                        name
                      }
                      ... on ProjectV2SingleSelectField {
                        id
                        name
                        options {
                          id
                          name
                        }
                      }
                    }
                  }
                }
              }
            }' -f owner=$PROJECT_OWNER -F number=$PROJECT_NUMBER)

          # 提取项目ID和字段配置
          project_id=$(echo "$project_data" | jq -r '.data.user.projectV2.id')
          status_field_id=$(echo "$project_data" | jq -r '.data.user.projectV2.fields.nodes[] | select(.name == "Status") | .id')
          todo_option_id=$(echo "$project_data" | jq -r '.data.user.projectV2.fields.nodes[] | select(.name == "Status") | .options[] | select(.name == "Todo") | .id')

          echo "project_id=$project_id" >> $GITHUB_OUTPUT
          echo "status_field_id=$status_field_id" >> $GITHUB_OUTPUT
          echo "todo_option_id=$todo_option_id" >> $GITHUB_OUTPUT

      - name: 处理Issue创建事件
        if: github.event_name == 'issues' && github.event.action == 'opened'
        env:
          GH_TOKEN: ${{ steps.app-token.outputs.token }}
          PROJECT_ID: ${{ steps.project-config.outputs.project_id }}
          STATUS_FIELD_ID: ${{ steps.project-config.outputs.status_field_id }}
          TODO_OPTION_ID: ${{ steps.project-config.outputs.todo_option_id }}
        run: |
          # 添加Issue到项目
          add_result=$(gh api graphql -f query='
            mutation($projectId: ID!, $contentId: ID!) {
              addProjectV2ItemById(input: {
                projectId: $projectId
                contentId: $contentId
              }) {
                item {
                  id
                }
              }
            }' -f projectId="$PROJECT_ID" -f contentId="${{ github.event.issue.node_id }}")

          item_id=$(echo "$add_result" | jq -r '.data.addProjectV2ItemById.item.id')

          # 设置状态为Todo
          if [ -n "$STATUS_FIELD_ID" ] && [ -n "$TODO_OPTION_ID" ]; then
            gh api graphql -f query='
              mutation($projectId: ID!, $itemId: ID!, $fieldId: ID!, $optionId: String!) {
                updateProjectV2ItemFieldValue(input: {
                  projectId: $projectId
                  itemId: $itemId
                  fieldId: $fieldId
                  value: {
                    singleSelectOptionId: $optionId
                  }
                }) {
                  projectV2Item {
                    id
                  }
                }
              }' -f projectId="$PROJECT_ID" -f itemId="$item_id" -f fieldId="$STATUS_FIELD_ID" -f optionId="$TODO_OPTION_ID"
          fi
```

### 4.5 核心脚本示例

**start-next-task.sh 关键逻辑：**
```bash
#!/bin/bash

# 获取当前任务状态
get_current_task() {
    if [ -f "$AUTOMATION_STATE_DIR/current-task.json" ]; then
        local content=$(cat "$AUTOMATION_STATE_DIR/current-task.json" 2>/dev/null || echo "null")
        # 验证JSON格式
        if echo "$content" | jq . > /dev/null 2>&1; then
            echo "$content"
        else
            echo "null"
        fi
    else
        echo "null"
    fi
}

# 查询待办任务列表
get_todo_issues() {
    gh api graphql -f query='
      query($owner: String!, $number: Int!) {
        user(login: $owner) {
          projectV2(number: $number) {
            items(first: 50, orderBy: {field: POSITION, direction: ASC}) {
              nodes {
                id
                content {
                  ... on Issue {
                    id
                    number
                    title
                    state
                  }
                }
                fieldValues(first: 10) {
                  nodes {
                    ... on ProjectV2ItemFieldSingleSelectValue {
                      name
                      field {
                        ... on ProjectV2SingleSelectField {
                          name
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }' -f owner="$PROJECT_OWNER" -F number="$PROJECT_NUMBER" | \
    jq -r '.data.user.projectV2.items.nodes[]? |
           select(.content.state == "OPEN") |
           select(.fieldValues.nodes[]? | select(.field.name == "Status" and .name == "Todo")) |
           {id: .id, issueId: .content.id, issueNumber: .content.number, title: .content.title}'
}

# 开始下一个任务
start_next_task() {
    log_info "🔍 查找下一个待办任务..."

    # 检查是否已有正在进行的任务
    local current_task=$(get_current_task)
    if [ "$current_task" != "null" ] && [ -n "$current_task" ]; then
        if echo "$current_task" | jq . > /dev/null 2>&1; then
            local current_issue_number=$(echo "$current_task" | jq -r '.issueNumber // empty')
            if [ -n "$current_issue_number" ] && [ "$current_issue_number" != "null" ]; then
                log_warning "已有正在进行的任务: #$current_issue_number"
                return 1
            fi
        fi
    fi

    # 获取第一个待办任务
    local next_task=$(get_todo_issues | head -n 1)
    if [ -z "$next_task" ] || [ "$next_task" = "null" ]; then
        log_info "📭 没有找到待办任务"
        return 0
    fi

    local item_id=$(echo "$next_task" | jq -r '.id')
    local issue_number=$(echo "$next_task" | jq -r '.issueNumber')
    local title=$(echo "$next_task" | jq -r '.title')

    log_info "🎯 开始任务: #$issue_number - $title"

    # 更新状态为"进行中"
    update_item_status "$item_id" "$IN_PROGRESS_OPTION_ID"

    # 保存当前任务信息
    echo "$next_task" > "$AUTOMATION_STATE_DIR/current-task.json"

    log_success "✅ 任务已开始: #$issue_number"
}
```

### 4.6 部署验证

**验证步骤：**
1. **配置验证**：
   ```bash
   # 检查Secret配置
   gh secret list

   # 验证项目访问权限
   gh api graphql -f query='query { viewer { login } }'

   # 测试项目查询
   gh api graphql -f query='
     query($owner: String!, $number: Int!) {
       user(login: $owner) {
         projectV2(number: $number) {
           id
           title
         }
       }
     }' -f owner="Amoresdk" -F number=1
   ```

2. **功能测试**：
   ```bash
   # 创建测试Issue
   gh issue create --title "测试自动化" --body "验证自动化系统功能"

   # 手动触发workflow
   gh workflow run task-automation.yml -f operation=start-next-task

   # 查看运行状态
   gh run list --workflow=task-automation.yml --limit=1
   ```

3. **错误排查**：
   ```bash
   # 查看最新workflow日志
   gh run view --log

   # 检查特定job的日志
   gh run view <run-id> --job=<job-id> --log

   # 验证GraphQL查询
   gh api graphql -f query='...' --jq '.errors'
   ```

### 4.7 常见故障排查

**问题1：Secret未配置或无效**
```
❌ 错误：未找到CHAIGUANJIA_PAT Secret
```
**解决方案：**
- 检查Secret名称是否正确（不能以GITHUB_开头）
- 验证PAT权限包含repo和project
- 确认PAT未过期

**问题2：项目访问权限不足**
```
❌ 错误：未找到项目 #1
```
**解决方案：**
- 确认项目编号正确
- 验证PAT对项目的访问权限
- 检查项目是否为个人项目而非组织项目

**问题3：GraphQL查询错误**
```
Variable $issueNumber is declared but not used
```
**解决方案：**
- 移除未使用的变量声明
- 检查查询语法是否正确
- 使用GitHub GraphQL Explorer验证查询

**问题4：jq解析错误**
```
Cannot iterate over null
```
**解决方案：**
- 在数组访问时添加`?`操作符
- 检查API响应是否包含预期数据
- 添加null值检查逻辑

---

## 5. 团队协作规范

### 5.1 新成员上手指南

**快速开始清单：**
1. [ ] 克隆仓库到本地
2. [ ] 了解项目看板结构和字段含义
3. [ ] 熟悉Issue创建和标签使用规范
4. [ ] 学习基本的GitHub Actions workflow触发方法
5. [ ] 掌握常用的故障排查命令

**权限要求：**
- 仓库的Write权限（用于创建Issue和PR）
- 项目的Write权限（用于手动调整状态）

### 5.2 日常使用规范

**Issue创建规范：**
```markdown
# Issue标题格式
[类型] 简洁描述功能或问题

# 示例
feat: 添加用户登录功能
fix: 修复数据导出错误
docs: 更新API文档
test: 添加单元测试

# Issue描述模板
## 需求描述
详细描述功能需求或问题现象

## 验收标准
- [ ] 标准1
- [ ] 标准2
- [ ] 标准3

## 技术要求
- 使用的技术栈
- 性能要求
- 兼容性要求

## 相关资源
- 设计稿链接
- 参考文档
- 相关Issue
```

**标签使用规范：**
- `priority:high` - 高优先级任务
- `priority:medium` - 中优先级任务
- `priority:low` - 低优先级任务
- `type:feature` - 新功能开发
- `type:bug` - 问题修复
- `type:docs` - 文档相关
- `type:test` - 测试相关
- `status:blocked` - 被阻塞的任务
- `status:review` - 需要评审

**任务流转规范：**
1. **自动化优先**：依赖系统自动更新状态
2. **手动干预**：仅在特殊情况下手动调整
3. **状态同步**：确保GitHub Projects与实际进度一致
4. **及时关闭**：完成后立即关闭Issue

**分支管理规范：**
```bash
# 分支命名格式
feature/issue<number>-<description>
fix/issue<number>-<description>
docs/issue<number>-<description>

# 示例
feature/issue15-user-authentication
fix/issue23-data-export-error
docs/issue8-api-documentation
```

### 5.3 维护和更新

**定期维护清单：**

**每日检查：**
- [ ] 查看当日workflow运行状态
- [ ] 检查是否有失败的自动化任务
- [ ] 确认新创建的Issue已正确添加到项目

**每周维护：**
- [ ] 清理已完成超过30天的Issue
- [ ] 检查项目看板的整体状态
- [ ] 更新优先级标签
- [ ] 检查PAT token有效期

**每月维护：**
- [ ] 备份项目配置文件
- [ ] 检查自动化脚本性能
- [ ] 更新文档和最佳实践
- [ ] 评估系统扩展需求

**版本更新流程：**
```bash
# 1. 创建feature分支
git checkout -b feature/automation-improvement

# 2. 进行修改和测试
# 修改相关文件...

# 3. 本地测试
./scripts/test-automation.sh

# 4. 提交变更
git add .
git commit -m "feat(automation): 改进任务自动分配逻辑"

# 5. 推送并创建PR
git push origin feature/automation-improvement
gh pr create --title "改进任务自动分配逻辑" --body "详细描述变更内容"

# 6. 合并后清理
git checkout main
git pull origin main
git branch -d feature/automation-improvement
```

**配置备份策略：**
```bash
# 备份脚本示例
#!/bin/bash
BACKUP_DIR="backups/$(date +%Y%m%d)"
mkdir -p "$BACKUP_DIR"

# 备份配置文件
cp .github/project-config.json "$BACKUP_DIR/"
cp -r .github/workflows/ "$BACKUP_DIR/"
cp -r scripts/ "$BACKUP_DIR/"

# 备份项目状态
gh api graphql -f query='...' > "$BACKUP_DIR/project-state.json"

echo "✅ 配置已备份到 $BACKUP_DIR"
```

**监控和告警：**
```yaml
# 添加到workflow中的监控步骤
- name: 发送失败通知
  if: failure()
  run: |
    # 发送邮件或Slack通知
    echo "自动化系统运行失败，请检查日志"
    # 可以集成具体的通知服务
```

---

## 6. 经验教训与最佳实践

### 6.1 关键决策点分析

**决策1：选择个人项目而非组织项目**
- **原因**：项目规模适中，个人项目功能足够
- **优势**：配置简单，权限清晰
- **劣势**：扩展性相对有限

**决策2：使用PAT而非GitHub App**
- **原因**：个人项目对GitHub App支持有限
- **优势**：配置简单，维护成本低
- **劣势**：权限粒度较粗

**决策3：采用事件驱动的自动化**
- **原因**：实时响应，减少手动操作
- **优势**：效率高，一致性好
- **劣势**：调试相对复杂

### 6.2 避免重复错误的建议

**开发阶段：**
1. **先验证API**：在编写workflow前，先用gh cli验证GraphQL查询
2. **渐进式开发**：从简单功能开始，逐步增加复杂性
3. **充分测试**：每个功能都要有对应的测试用例

**部署阶段：**
1. **环境隔离**：使用测试仓库验证功能
2. **配置检查**：部署前仔细检查所有配置项
3. **监控日志**：密切关注初期运行日志

**维护阶段：**
1. **文档同步**：代码变更时及时更新文档
2. **版本管理**：重要变更要打tag标记
3. **备份配置**：定期备份重要配置文件

### 6.3 性能优化建议

**GraphQL查询优化：**
- 只查询必需的字段
- 使用适当的分页参数
- 避免嵌套过深的查询

**Workflow优化：**
- 合理使用缓存
- 避免不必要的API调用
- 优化脚本执行效率

### 6.4 未来扩展方向

**功能扩展：**
- 支持更多项目字段类型
- 添加自动化报告功能
- 集成更多外部工具

**架构优化：**
- 考虑迁移到GitHub App（如果需要）
- 实现更细粒度的权限控制
- 添加更完善的错误处理

**团队协作：**
- 开发可视化的配置界面
- 添加团队成员权限管理
- 实现更灵活的工作流定制

---

## 附录

### A. 常用命令参考

**GitHub CLI基础命令：**
```bash
# 认证和配置
gh auth login
gh auth status
gh config set editor vim

# 项目管理
gh api graphql -f query='query { viewer { login } }'
gh api graphql -f query='query($owner: String!, $number: Int!) {
  user(login: $owner) {
    projectV2(number: $number) { id title }
  }
}' -f owner="Amoresdk" -F number=1

# Workflow管理
gh workflow list
gh workflow run task-automation.yml
gh workflow run task-automation.yml -f operation=start-next-task
gh run list --workflow=task-automation.yml --limit=5
gh run view --log
gh run view <run-id> --job=<job-id> --log

# Issue管理
gh issue list --state=open --limit=10
gh issue create --title "标题" --body "描述"
gh issue close <issue-number>
gh issue view <issue-number>

# Secret管理
gh secret list
gh secret set CHAIGUANJIA_PAT --body="<token>"
gh secret delete CHAIGUANJIA_PAT
```

**故障排查命令集：**
```bash
# 系统状态检查
gh auth status
gh api rate_limit
gh api graphql -f query='query { viewer { login } }'

# 项目访问验证
gh api graphql -f query='
  query($owner: String!, $number: Int!) {
    user(login: $owner) {
      projectV2(number: $number) {
        id
        title
        url
      }
    }
  }' -f owner="Amoresdk" -F number=1

# Workflow调试
gh run list --workflow=project-automation.yml --limit=3
gh run view --log | grep -E "(ERROR|FAIL|❌)"
gh workflow view task-automation.yml

# 项目状态查询
gh api graphql -f query='
  query($owner: String!, $number: Int!) {
    user(login: $owner) {
      projectV2(number: $number) {
        items(first: 10) {
          nodes {
            content {
              ... on Issue {
                number
                title
                state
              }
            }
            fieldValues(first: 5) {
              nodes {
                ... on ProjectV2ItemFieldSingleSelectValue {
                  name
                  field {
                    ... on ProjectV2SingleSelectField {
                      name
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }' -f owner="Amoresdk" -F number=1 | jq '.data.user.projectV2.items.nodes[]'
```

**脚本调试命令：**
```bash
# 本地脚本测试
cd scripts/
bash -x start-next-task.sh  # 调试模式运行
./show-status.sh            # 查看当前状态
./sync-status.sh --dry-run  # 干运行模式

# 环境变量设置
export PROJECT_OWNER="Amoresdk"
export PROJECT_NUMBER=1
export GH_TOKEN="<your-token>"

# 日志分析
grep -E "(ERROR|WARN|FAIL)" automation-state/logs/*.log
tail -f automation-state/logs/automation.log
```

### B. GraphQL查询模板

**查询项目基本信息：**
```graphql
query($owner: String!, $number: Int!) {
  user(login: $owner) {
    projectV2(number: $number) {
      id
      title
      url
      shortDescription
      fields(first: 20) {
        nodes {
          ... on ProjectV2Field {
            id
            name
            dataType
          }
          ... on ProjectV2SingleSelectField {
            id
            name
            options {
              id
              name
              color
            }
          }
        }
      }
    }
  }
}
```

**查询项目条目：**
```graphql
query($owner: String!, $number: Int!) {
  user(login: $owner) {
    projectV2(number: $number) {
      items(first: 50, orderBy: {field: POSITION, direction: ASC}) {
        nodes {
          id
          content {
            ... on Issue {
              id
              number
              title
              state
              url
              createdAt
              updatedAt
            }
            ... on PullRequest {
              id
              number
              title
              state
              url
            }
          }
          fieldValues(first: 10) {
            nodes {
              ... on ProjectV2ItemFieldSingleSelectValue {
                name
                field {
                  ... on ProjectV2SingleSelectField {
                    name
                  }
                }
              }
              ... on ProjectV2ItemFieldTextValue {
                text
                field {
                  ... on ProjectV2Field {
                    name
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
```

**添加条目到项目：**
```graphql
mutation($projectId: ID!, $contentId: ID!) {
  addProjectV2ItemById(input: {
    projectId: $projectId
    contentId: $contentId
  }) {
    item {
      id
    }
  }
}
```

**更新条目字段值：**
```graphql
mutation($projectId: ID!, $itemId: ID!, $fieldId: ID!, $optionId: String!) {
  updateProjectV2ItemFieldValue(input: {
    projectId: $projectId
    itemId: $itemId
    fieldId: $fieldId
    value: {
      singleSelectOptionId: $optionId
    }
  }) {
    projectV2Item {
      id
    }
  }
}
```

### C. 快速参考表

**项目配置参数：**
| 参数 | 说明 | 示例值 |
|------|------|--------|
| PROJECT_OWNER | 项目所有者 | Amoresdk |
| PROJECT_NUMBER | 项目编号 | 1 |
| PROJECT_NAME | 项目名称 | 柴管家开发任务看板 |
| CHAIGUANJIA_PAT | 访问令牌 | ghp_xxxxxxxxxxxx |

**状态字段选项：**
| 状态 | 英文名 | 中文名 | 说明 |
|------|--------|--------|------|
| Todo | Todo | 待办 | 新创建的任务 |
| In Progress | In Progress | 进行中 | 正在开发的任务 |
| Done | Done | 已完成 | 已完成的任务 |

**常用文件路径：**
| 文件 | 路径 | 用途 |
|------|------|------|
| 项目自动化 | `.github/workflows/project-automation.yml` | Issue/PR自动同步 |
| 任务自动化 | `.github/workflows/task-automation.yml` | 任务管理 |
| 项目配置 | `.github/project-config.json` | 配置参数 |
| 开始任务 | `scripts/start-next-task.sh` | 开始下一个任务 |
| 完成任务 | `scripts/complete-task.sh` | 完成当前任务 |

**错误代码对照：**
| 错误信息 | 原因 | 解决方案 |
|----------|------|----------|
| `Secret not found` | PAT未配置 | 配置CHAIGUANJIA_PAT Secret |
| `Project not found` | 项目访问权限不足 | 检查PAT权限和项目编号 |
| `Field doesn't exist` | GraphQL字段错误 | 检查API文档，更新查询 |
| `Cannot iterate over null` | jq解析null值 | 添加`?`操作符处理null |

### D. 相关资源链接

**官方文档：**
- [GitHub Projects V2 API文档](https://docs.github.com/en/graphql/reference/objects#projectv2)
- [GitHub Actions文档](https://docs.github.com/en/actions)
- [GitHub CLI文档](https://cli.github.com/manual/)
- [GraphQL查询语法](https://graphql.org/learn/queries/)

**工具和资源：**
- [GitHub GraphQL Explorer](https://docs.github.com/en/graphql/overview/explorer)
- [jq命令手册](https://stedolan.github.io/jq/manual/)
- [YAML语法参考](https://yaml.org/spec/1.2/spec.html)
- [Bash脚本指南](https://www.gnu.org/software/bash/manual/)

**社区资源：**
- [GitHub Community Discussions](https://github.com/community/community/discussions)
- [GitHub Actions Marketplace](https://github.com/marketplace?type=actions)
- [Awesome GitHub Actions](https://github.com/sdras/awesome-actions)

---

## 更新日志

### v1.0 (2025-08-02)
- 初始版本发布
- 完整的自动化系统实现
- 详细的部署和维护指南
- 全面的故障排查方案

---

*本文档版本：v1.0*
*最后更新：2025-08-02*
*维护者：Augment Agent*
*项目：柴管家 GitHub Projects自动化系统*
