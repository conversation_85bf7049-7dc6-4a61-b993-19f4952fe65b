# **柴管家开发方案 V1.0**

**文档状态：** 已确认  
**创建日期：** 2025-08-02  
**版本：** 1.0  
**负责人：** AI开发团队

---

## **1. 项目概述**

### **1.1 产品愿景**
为知识类、教培类个人IP运营者提供一站式私域运营解决方案，通过聚合多平台消息和AI能力，实现降本增效、激活社群、深化用户关系的目标。

### **1.2 核心问题与价值**
- **消息分散，效率低下**：运营者需在微信、抖音、小红书等多个平台间频繁切换
- **重复性咨询繁重**：大量用户反复询问相似的基础问题
- **社群活跃度维护难**：运营者精力有限，难以持续维护多个社群
- **用户关系维护难**：无法形成统一用户视图，难以进行精细化运营

### **1.3 目标用户**
- **知识IP主理人**：如职场成长博主，需要从重复咨询中解放精力
- **教培机构IP运营**：如考研机构运营，需要从海量用户中筛选高意向线索

---

## **2. 开发哲学与方法论**

### **2.1 BDD驱动开发**
严格遵循**行为驱动开发（BDD）**三步走流程：

```mermaid
graph TD
    A[1. 编写失败测试] --> B[2. 编写产品代码]
    B --> C[3. 通过测试]
    C --> D{所有验收标准完成?}
    D -- 否 --> A
    D -- 是 --> E[功能完成]
```

### **2.2 核心开发原则**
1. **单一信息源原则**：GitHub是项目管理的唯一真实信息源
2. **BDD驱动原则**：绝不允许在没有对应失败测试的情况下编写产品代码
3. **主干开发原则**：所有开发工作在功能分支进行，main分支始终可部署
4. **自动化一切原则**：所有检查通过CI/CD流水线强制执行

---

## **3. 技术架构**

### **3.1 架构选型**
**模块化单体 + 事件驱动连接器 (Modular Monolith with Event-Driven Connectors)**

```mermaid
graph TD
    subgraph "客户端"
        A["Web前端 (React SPA)"]
    end

    subgraph "服务端"
        B["核心后端服务 (Python/FastAPI)"]
        C["消息队列 (RabbitMQ)"]
        D["主数据库 (PostgreSQL)"]
        E["向量数据库 (ChromaDB)"]

        subgraph "连接器集群"
            F1["闲鱼连接器"]
            F2["微信连接器"]
            F3["抖音连接器"]
        end
    end

    subgraph "第三方平台"
        G1["闲鱼"]
        G2["微信"]
        G3["抖音"]
    end

    A -- "HTTPS/REST API & WebSocket" --> B
    B -- "读写" --> D
    B -- "读写/索引" --> E
    B -- "生产/消费" --> C

    F1 <--> G1
    F2 <--> G2
    F3 <--> G3

    F1 -- "异步消息" --> C
    F2 -- "异步消息" --> C
    F3 -- "异步消息" --> C
```

### **3.2 技术栈**
| 领域 | 技术选型 | 理由 |
|------|----------|------|
| **后端** | Python + FastAPI | 团队熟悉，高性能异步IO |
| **前端** | React + Vite | 团队熟悉，开发体验佳 |
| **主数据库** | PostgreSQL | 功能强大，支持复杂查询 |
| **向量数据库** | ChromaDB | Python原生，轻量易部署 |
| **消息队列** | RabbitMQ | 成熟可靠，支持事件驱动 |
| **部署** | Docker + Docker Compose | 环境一致性，简化部署 |

---

## **4. MVP史诗分解**

### **4.1 史诗概览**

```mermaid
graph TD
    A[MVP核心闭环] --> B[史诗1: 核心渠道管理]
    A --> C[史诗2: 统一消息工作台]
    A --> D[史诗3: AI副驾与知识库]
    A --> E[史诗4: AI托管与人工接管]
    
    B --> B1[Issue 1.1: 接入闲鱼账号]
    B --> B2[Issue 1.2: 管理多个账号]
    B --> B3[Issue 1.3: 设置账号别名]
    B --> B4[Issue 1.4: 监控连接状态]
    
    C --> C1[Issue 2.1: 统一会话列表]
    C --> C2[Issue 2.2: 工作台回复消息]
    C --> C3[Issue 2.3: 实时消息同步]
    C --> C4[Issue 2.4: 会话历史记录]
    
    D --> D1[Issue 3.1: 知识库管理]
    D --> D2[Issue 3.2: AI意图分析]
    D --> D3[Issue 3.3: AI回复建议]
    D --> D4[Issue 3.4: 一键采用建议]
    
    E --> E1[Issue 4.1: AI托管模式]
    E --> E2[Issue 4.2: 置信度自动回复]
    E --> E3[Issue 4.3: 低置信度转交]
    E --> E4[Issue 4.4: 无缝人工接管]
```

### **4.2 史诗详细定义**

#### **史诗1：核心渠道管理**
**目标**：构建系统的基础连接能力，允许用户安全接入多平台账号并进行统一管理。

#### **史诗2：统一消息工作台**
**目标**：提供核心操作界面，实时汇聚所有渠道消息，支持跨平台统一回复。

#### **史诗3：AI副驾与知识库**
**目标**：为人工回复提效赋能，提供知识库管理和AI辅助回复功能。

#### **史诗4：AI托管与人工接管**
**目标**：实现运营自动化，支持AI托管模式和智能人工接管机制。

---

## **5. Sprint规划与时间线**

### **5.1 开发路线图**

```mermaid
gantt
    title 柴管家开发路线图
    dateFormat  YYYY-MM-DD
    axisFormat %m-%d

    section 基础设施 (M0)
    环境搭建与CI/CD (S0) :done, 2025-08-04, 1w

    section MVP核心闭环 (M1)
    闲鱼连接器与消息收发 (S1) :active, 2025-08-11, 2w
    AI副驾与知识库 (S2) :2025-08-25, 2w
    AI托管与人工接管 (S3) :2025-09-08, 2w

    section 质量保证
    集成测试与优化 (S4) :2025-09-22, 1w
```

### **5.2 Sprint详细规划**

#### **Sprint 0：环境搭建与CI/CD (1周)**
**目标**：建立开发基础设施
- 项目初始化和代码仓库设置
- Docker开发环境配置
- CI/CD流水线搭建
- 基础架构代码框架

#### **Sprint 1：闲鱼连接器与消息收发 (2周)**
**目标**：建立端到端基础流程
- Issue 1.1：用户可以接入闲鱼账号
- Issue 2.1：用户可以查看统一会话列表
- Issue 2.2：用户可以在工作台回复消息
- Issue 2.3：系统可以实时同步消息

#### **Sprint 2：AI副驾与知识库 (2周)**
**目标**：构建AI辅助能力
- Issue 3.1：用户可以管理知识库问答对
- Issue 3.2：AI可以分析用户意图
- Issue 3.3：AI可以生成回复建议
- Issue 3.4：用户可以一键采用AI建议

#### **Sprint 3：AI托管与人工接管 (2周)**
**目标**：完成人机协作流程
- Issue 4.1：用户可以开启AI托管模式
- Issue 4.2：AI可以基于置信度自动回复
- Issue 4.3：系统可以在低置信度时自动转交人工
- Issue 4.4：用户可以无缝接管AI会话

---

## **6. 关键Issue详细定义**

### **6.1 Issue 1.1：用户可以接入闲鱼账号**

#### **用户故事**
作为一个IP运营者，我希望能将我的闲鱼账号接入到"柴管家"中，这样我就能在统一平台管理闲鱼的客户咨询。

#### **验收标准 (Gherkin格式)**
```gherkin
功能: 闲鱼账号接入
  作为 IP运营者
  我想要 接入我的闲鱼账号
  以便于 在柴管家中统一管理消息

  场景: 成功接入闲鱼账号
    假如 我是一个已登录的用户
    当 我访问渠道管理页面
    并且 我点击"添加新账号"按钮
    并且 我选择"闲鱼"平台
    并且 我完成闲鱼账号授权流程
    那么 系统应该显示"账号接入成功"
    并且 我应该能在账号列表中看到新接入的闲鱼账号
    并且 账号状态应该显示为"已连接"

  场景: 授权失败处理
    假如 我是一个已登录的用户
    当 我尝试接入闲鱼账号
    但是 授权过程失败
    那么 系统应该显示明确的错误信息
    并且 我应该能重新尝试授权
```

#### **技术实现要点**
- 开发闲鱼连接器，实现账号授权和状态监控
- 设计渠道管理API端点
- 实现前端授权流程界面
- 建立消息队列通信机制

### **6.2 Issue 2.1：用户可以查看统一会话列表**

#### **用户故事**
作为一个IP运营者，我希望能在一个统一的界面看到所有已接入账号的会话，这样我就不用在各个App之间切换。

#### **验收标准 (Gherkin格式)**
```gherkin
功能: 统一会话列表
  作为 IP运营者
  我想要 查看所有平台的会话
  以便于 统一管理客户沟通

  场景: 查看会话列表
    假如 我已经接入了闲鱼账号
    并且 该账号有3个活跃会话
    当 我访问工作台页面
    那么 我应该能看到会话列表
    并且 列表中应该显示3个会话
    并且 每个会话应该显示联系人名称、最新消息摘要、时间戳
    并且 每个会话应该标明来源平台（闲鱼）

  场景: 会话排序
    假如 我有多个会话
    当 我查看会话列表
    那么 会话应该按最新消息时间倒序排列
    并且 有未读消息的会话应该显示未读数角标
```

#### **技术实现要点**
- 设计conversations表结构
- 实现会话列表API
- 开发前端会话列表组件
- 建立WebSocket实时更新机制

### **6.3 Issue 3.2：AI可以分析用户意图**

#### **用户故事**
作为一个IP运营者，我希望AI能实时分析客户的意图，这样我就能更好地理解客户需求并提供针对性回复。

#### **验收标准 (Gherkin格式)**
```gherkin
功能: AI意图分析
  作为 IP运营者
  我想要 AI分析客户意图
  以便于 更好地理解客户需求

  场景: 价格咨询意图识别
    假如 我正在查看一个会话
    当 客户发送消息"你们的课程多少钱？"
    那么 AI应该识别出意图为"价格咨询"
    并且 在智能看板中显示意图分析结果
    并且 置信度应该大于0.8

  场景: 复杂意图识别
    假如 我正在查看一个会话
    当 客户发送消息"我想了解一下你们的课程，有什么优惠吗？"
    那么 AI应该识别出主要意图为"课程咨询"
    并且 次要意图为"优惠询问"
    并且 在智能看板中显示分析结果
```

#### **技术实现要点**
- 集成大语言模型API
- 设计意图分析提示词模板
- 实现意图分析服务
- 开发智能看板前端组件

### **6.4 Issue 4.3：系统可以在低置信度时自动转交人工**

#### **用户故事**
作为一个IP运营者，我希望当AI对回复没有把握时，它能自动停下来并向我求助，这样既能保证效率又能确保服务质量。

#### **验收标准 (Gherkin格式)**
```gherkin
功能: 低置信度自动转交
  作为 IP运营者
  我想要 AI在低置信度时自动转交人工
  以便于 确保服务质量

  场景: 低置信度自动转交
    假如 一个会话处于"AI托管模式"
    并且 系统设定的置信度阈值为0.8
    当 AI针对新消息生成了一条置信度为0.6的回复
    那么 系统绝不能自动发送该回复
    并且 该会话的状态必须自动切换为"待人工接管"
    并且 系统必须在界面上高亮该会话以通知运营者
    并且 我应该收到桌面通知

  场景: 高置信度自动回复
    假如 一个会话处于"AI托管模式"
    并且 系统设定的置信度阈值为0.8
    当 AI针对新消息生成了一条置信度为0.9的回复
    那么 系统应该自动发送该回复
    并且 会话状态保持"AI托管中"
    并且 回复记录应该标记为AI发送
```

#### **技术实现要点**
- 实现置信度评估算法
- 设计会话状态管理机制
- 开发自动转交逻辑
- 实现通知系统

---

## **7. 开发工作流实施**

### **7.1 GitHub工作流**

```mermaid
graph TD
    A[GitHub项目板] --> B[待办 To Do]
    B --> C[进行中 In Progress]
    C --> D[已完成 Done]

    E[AI开发者] --> F[领取Issue]
    F --> G[创建功能分支]
    G --> H[BDD三步走开发]
    H --> I[提交代码]
    I --> J[创建PR]
    J --> K[CI/CD检查]
    K --> L[人工审查]
    L --> M[合并到main]
    M --> N[自动关闭Issue]

    F -.-> B
    G -.-> C
    N -.-> D
```

### **7.2 分支命名规范**
- **格式**：`feature/issue<ISSUE_NUMBER>-<short-description>`
- **示例**：`feature/issue1-connect-xianyu-account`

### **7.3 提交信息规范**
- **格式**：`<type>(<scope>): <subject>`
- **示例**：
  - `feat(api): 新增用于创建渠道账号的端点`
  - `fix(ui): 修正登录页面的按钮对齐问题`
  - `test(api): 为创建账号功能添加集成测试`

### **7.4 PR描述规范**
- **标题**：使用中文，与Issue标题保持一致
- **正文**：
  - 使用`Closes #<ISSUE_NUMBER>`关联Issue
  - 包含简短的中文实现说明
  - 包含测试清单，引用DoD标准

---

## **8. 质量保证策略**

### **8.1 完成的定义 (DoD)**
所有用户故事必须满足以下条件才能被视为"已完成"：

1. ✅ 代码已通过CI/CD流水线的所有自动化检查
2. ✅ 所有验收标准都已通过自动化测试
3. ✅ 单元测试已编写并通过，核心逻辑代码覆盖率≥85%
4. ✅ 代码已成功合并到main分支
5. ✅ 相关API文档已自动生成并更新

### **8.2 测试策略**

```mermaid
graph TD
    A[测试金字塔] --> B[单元测试]
    A --> C[集成测试]
    A --> D[端到端测试]

    B --> B1[业务逻辑测试]
    B --> B2[API端点测试]
    B --> B3[组件测试]

    C --> C1[数据库集成测试]
    C --> C2[消息队列集成测试]
    C --> C3[第三方API集成测试]

    D --> D1[用户流程测试]
    D --> D2[跨平台兼容性测试]
```

### **8.3 CI/CD流水线**
- **代码风格检查**：ESLint (前端) + Black (后端)
- **单元测试**：Jest (前端) + pytest (后端)
- **集成测试**：API测试 + 数据库测试
- **构建测试**：Docker镜像构建
- **安全扫描**：依赖漏洞检查

---

## **9. 风险管理与应对策略**

### **9.1 技术风险**

| 风险 | 优先级 | 应对策略 |
|------|--------|----------|
| **第三方接口稳定性** | 高 | 架构隔离：连接器独立，故障不影响核心<br>自动化监控：集成测试及时发现接口变更 |
| **AI效果不及预期** | 中 | BDD精确指令：Gherkin约束AI行为<br>DoD质量门禁：自动化检查代码质量 |
| **闲鱼反爬虫机制** | 高 | 模拟真实用户行为<br>实现请求频率控制<br>准备备用方案 |

### **9.2 项目风险**

| 风险 | 优先级 | 应对策略 |
|------|--------|----------|
| **需求蔓延** | 中 | 路线图防火墙：新需求进入待办列表评估<br>价值导向：通过替换而非增加控制工作量 |
| **技术债务积累** | 中 | 定期重构Sprint<br>代码审查强制执行<br>技术债务可视化跟踪 |

---

## **10. 交付物与验收标准**

### **10.1 后端交付物**
每个后端用户故事完成时必须包含：
1. **可交互的API文档** (Swagger UI)
2. **专用测试面板** (简易HTML页面用于手动API调用)
3. **冒烟测试报告** (来自CI/CD流水线)
4. **结构化日志输出**

### **10.2 前端交付物**
每个前端用户故事完成时必须包含：
1. **可操作的预览环境** (部署在Staging环境)
2. **端到端测试报告**
3. **基于AC的用户验收清单**

### **10.3 里程碑交付物**
- **Sprint 1结束**：可工作的闲鱼消息收发原型
- **Sprint 2结束**：具备AI辅助功能的工作台
- **Sprint 3结束**：完整的MVP产品
- **项目结束**：生产就绪的柴管家系统

---

## **11. 总结**

本开发方案基于BDD驱动开发理念，严格遵循GitHub工作流规范，通过4个史诗16个Issue的系统性分解，确保柴管家MVP能够按时、按质交付。

关键成功因素：
- ✅ **严格的BDD流程**：确保每个功能都有明确的验收标准
- ✅ **完善的自动化**：CI/CD流水线保证代码质量
- ✅ **风险前置管理**：技术风险在架构设计阶段就已考虑
- ✅ **可验证的交付**：每个Sprint都有明确的可演示交付物

通过这套方案，我们将构建一个高质量、可扩展、符合用户需求的柴管家系统，为个人IP运营者提供真正有价值的私域运营解决方案。
